#!/usr/bin/env python3
"""
测试成本核算系统的新实现
基于库存变化记录来计算成本
"""

import asyncio
from datetime import date, datetime
from tortoise import Tortoise

from app.models.food_stuff import FoodStuffStoreRecord, FoodStuff, Unit
from app.controllers.daily_cost_report import daily_cost_report_controller


async def test_cost_calculation():
    """测试成本核算计算"""
    print("开始测试成本核算系统...")

    # 测试日期
    test_date = date.today()
    school_id = 1
    recipe_id = 1

    print(f"测试日期: {test_date}")
    print(f"学校ID: {school_id}")
    print(f"食谱ID: {recipe_id}")

    try:
        # 查看当前的库存变化记录
        records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            created_at__date=test_date
        ).all()

        print(f"\n找到 {len(records)} 条库存变化记录:")
        for record in records:
            print(f"  记录ID: {record.id}")
            print(f"  操作类型: {record.operation_type}")
            print(f"  订单ID: {record.order_id}")
            print(f"  食谱ID: {record.recipe_id}")
            print(f"  创建时间: {record.created_at}")

            # 详细检查食材数据中的价格和订单信息
            for food_data in record.food_stuff_data:
                print(f"    食材ID: {food_data.get('food_stuff_id')}")
                print(f"    食材名称: {food_data.get('food_stuff_name')}")
                print(f"    变化量: {food_data.get('change_count')}")
                print(f"    价格: {food_data.get('price', '未设置')}")
                print(f"    订单ID: {food_data.get('order_id', '未设置')}")
                print(f"    食谱ID: {food_data.get('recipe_id', '未设置')}")
                print(f"    订单ID列表: {food_data.get('order_ids', '未设置')}")
                print(f"    食谱ID列表: {food_data.get('recipe_ids', '未设置')}")
                print("    ...")
            print("  ---")
        
        # 测试成本计算
        print(f"\n开始计算成本...")
        cost_details = await daily_cost_report_controller._calculate_daily_cost(
            report_date=test_date,
            school_id=school_id,
            recipe_id=recipe_id
        )
        
        print(f"\n成本计算结果:")
        print(f"找到 {len(cost_details)} 种食材的消耗记录")

        total_cost = 0

        for detail in cost_details:
            print(f"\n食材: {detail['food_stuff_name']} (ID: {detail['food_stuff_id']})")
            print(f"  单位: {detail['unit_name']}")
            print(f"  总消耗: {detail['total_quantity']} {detail['unit_name']}")
            print(f"  总成本: ¥{detail['total_cost']:.2f}")

            inventory_consume = detail['inventory_consume']
            order_consume = detail['order_consume']

            if inventory_consume['quantity'] > 0:
                print(f"  库存消耗: {inventory_consume['quantity']} {detail['unit_name']}, 成本: ¥{inventory_consume['cost']:.2f}")
                for order in inventory_consume['orders']:
                    print(f"    - 订单{order['order_id']}: {order['quantity']} {detail['unit_name']} × ¥{order['price']:.2f} (食谱{order['recipe_id']})")

            if order_consume['quantity'] > 0:
                print(f"  订单消耗: {order_consume['quantity']} {detail['unit_name']}, 成本: ¥{order_consume['cost']:.2f}")
                for order in order_consume['orders']:
                    print(f"    - 订单{order['order_id']}: {order['quantity']} {detail['unit_name']} × ¥{order['price']:.2f} (食谱{order['recipe_id']})")

            # 显示详细的消耗记录
            print(f"  详细消耗记录:")
            for i, consume_detail in enumerate(detail['consume_details']):
                print(f"    {i+1}. 记录ID{consume_detail['record_id']}: {consume_detail['operation_type']}")
                print(f"       数量: {consume_detail['quantity']}, 价格: ¥{consume_detail['price']:.2f}, 成本: ¥{consume_detail['cost']:.2f}")
                print(f"       订单ID: {consume_detail['order_id']}, 食谱ID: {consume_detail['recipe_id']}")
                if consume_detail['consume_order_id']:
                    print(f"       消耗订单ID: {consume_detail['consume_order_id']}, 消耗食谱ID: {consume_detail['consume_recipe_id']}")
                print(f"       时间: {consume_detail['created_at']}")

            total_cost += detail['total_cost']

        print(f"\n总成本: ¥{total_cost:.2f}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    # 初始化数据库连接
    from app.settings.config import settings
    
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        await test_cost_calculation()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
