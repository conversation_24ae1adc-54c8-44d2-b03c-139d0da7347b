<template>
  <CommonPage show-footer title="日成本核算单管理">
    <template #action>
      <NButton v-permission="'post/api/v1/daily-cost-report/generate'" type="primary" @click="handleAdd">
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />生成核算单
      </NButton>
      <NButton type="info" @click="showDebugModal = true" style="margin-left: 8px;">
        <TheIcon icon="material-symbols:bug-report" :size="18" class="mr-5" />调试数据
      </NButton>
    </template>

    <CrudTable ref="$table" v-model:query-items="queryItems" :columns="columns" :get-data="api.getDailyCostReportList">
      <template #queryBar>
        <QueryBarItem label="核算日期" :label-width="80">
          <NDatePicker v-model:formatted-value="queryItems.reportDate" value-format="yyyy-MM-dd" type="date"
            placeholder="选择核算日期" clearable @update:value="$table?.handleSearch()" />
        </QueryBarItem>
        <QueryBarItem label="日期范围" :label-width="80">
          <NDatePicker v-model:value="queryItems.dateRange" type="daterange" placeholder="选择日期范围" clearable
            @update:value="$table?.handleSearch()" />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="80">
          <NSelect v-model:value="queryItems.status" placeholder="选择状态" clearable :options="statusOptions"
            style="width: 120px" @update:value="$table?.handleSearch()" />
        </QueryBarItem>
      </template>
    </CrudTable>

    <CrudModal v-model:visible="modalVisible" :title="modalTitle" :loading="modalLoading" @save="handleSave">
      <NForm ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
        :rules="generateRules">
        <NFormItem label="核算日期" path="reportDate">
          <NDatePicker v-model:formatted-value="modalForm.reportDate" value-format="yyyy-MM-dd" type="date" placeholder="选择核算日期"
            style="width: 100%" @update:value="checkDateAvailable" />
        </NFormItem>
        <NFormItem label="食谱" path="recipeId">
          <NSelect v-model:value="modalForm.recipeId" placeholder="选择食谱（留空自动查找当日食谱）" clearable :options="recipeOptions"
            :loading="recipeLoading" />
        </NFormItem>
        <NAlert v-if="dateCheckMessage" :type="dateCheckType" :title="dateCheckMessage" class="mb-4" />
      </NForm>
    </CrudModal>

    <!-- 详情模态框 -->
    <NModal v-model:show="showDetailModal" preset="card" title="核算单详情" style="width: 80%; max-width: 1200px">
      <div v-if="currentReport">
        <NDescriptions :column="3" bordered>
          <NDescriptionsItem label="核算日期">{{ currentReport.report_date }}</NDescriptionsItem>
          <NDescriptionsItem label="食谱名称">{{ currentReport.recipe_name || '无' }}</NDescriptionsItem>
          <NDescriptionsItem label="状态">
            <NTag :type="currentReport.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentReport.status === 'confirmed' ? '已确认' : '草稿' }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem label="总成本">¥{{ currentReport.total_cost?.toFixed(2) || '0.00' }}</NDescriptionsItem>
          <NDescriptionsItem label="库存消耗成本">¥{{ currentReport.inventory_cost?.toFixed(2) || '0.00' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="订单消耗成本">¥{{ currentReport.order_cost?.toFixed(2) || '0.00' }}</NDescriptionsItem>
        </NDescriptions>

        <NDivider>食谱详情</NDivider>
        <div v-if="currentReport.meals && currentReport.meals.length > 0">
          <div v-for="meal in currentReport.meals" :key="meal.id" style="margin-bottom: 16px;">
            <NTag type="primary" size="medium" style="margin-bottom: 8px;">
              {{ getMealTypeName(meal.meal_type) }}
            </NTag>
            <div v-for="dish in meal.dish_list" :key="dish.dish_id" style="margin-left: 16px; margin-bottom: 12px;">
              <div style="font-weight: bold; margin-bottom: 4px; color: #333;">
                {{ dish.dish_name }}
              </div>
              <div style="margin-left: 16px; color: #666; font-size: 14px;">
                <span v-for="(ingredient, index) in dish.ingredients" :key="ingredient.food_stuff_id">
                  {{ ingredient.food_stuff_name }}({{ ingredient.quantity }}{{ ingredient.unit_name }})
                  <span v-if="index < dish.ingredients.length - 1">、</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <NAlert type="info" title="暂无食谱信息" />
        </div>

        <NDivider>成本明细</NDivider>
        <NDataTable :columns="detailColumns" :data="currentReport.cost_details || []" :pagination="false" />
      </div>
    </NModal>

    <!-- 调试模态框 -->
    <NModal v-model:show="showDebugModal" preset="card" title="调试数据查看" style="width: 90%; max-width: 1400px">
      <NForm ref="debugFormRef" :model="debugForm" label-placement="left" :label-width="100">
        <NFormItem label="调试日期" path="debugDate">
          <NDatePicker v-model:formatted-value="debugForm.debugDate" value-format="yyyy-MM-dd" type="date" placeholder="选择要调试的日期"
            style="width: 200px" />
        </NFormItem>
        <NFormItem label="食谱ID" path="recipeId">
          <NInputNumber v-model:value="debugForm.recipeId" placeholder="输入食谱ID（可选）" style="width: 200px" />
        </NFormItem>
        <NFormItem>
          <NButton type="primary" @click="handleDebugData" :loading="debugLoading">
            查看调试数据
          </NButton>
        </NFormItem>
      </NForm>

      <div v-if="debugData">
        <NDivider>调试信息</NDivider>
        <NDescriptions :column="3" bordered>
          <NDescriptionsItem label="日期">{{ debugData.date }}</NDescriptionsItem>
          <NDescriptionsItem label="学校ID">{{ debugData.school_id }}</NDescriptionsItem>
          <NDescriptionsItem label="食谱ID">{{ debugData.recipe_id || '无' }}</NDescriptionsItem>
          <NDescriptionsItem label="库存记录数">{{ debugData.store_records_count }}</NDescriptionsItem>
          <NDescriptionsItem label="订单数">{{ debugData.orders_count }}</NDescriptionsItem>
          <NDescriptionsItem label="已确认订单">{{ debugData.summary?.confirmed_orders || 0 }}</NDescriptionsItem>
          <NDescriptionsItem label="匹配食谱的订单">{{ debugData.summary?.orders_with_recipe_id || 0 }}</NDescriptionsItem>
          <NDescriptionsItem label="立即消耗订单">{{ debugData.summary?.orders_with_immediate_consume || 0 }}
          </NDescriptionsItem>
        </NDescriptions>

        <NDivider>消耗分析</NDivider>
        <NAlert type="info" style="margin-bottom: 16px;">
          <div>
            <p><strong>库存消耗</strong>：从现有库存中直接扣除的食材</p>
            <p><strong>订单消耗</strong>：库存不足时下单采购，入库后立即消耗的食材</p>
          </div>
        </NAlert>

        <NDivider>库存记录</NDivider>
        <NDataTable :columns="debugStoreColumns" :data="debugData.store_records || []" :pagination="{ pageSize: 5 }"
          max-height="300" />

        <NDivider>订单分析</NDivider>
        <NDataTable :columns="debugOrderAnalysisColumns" :data="debugData.order_analysis || []"
          :pagination="{ pageSize: 5 }" max-height="400" />

        <NDivider>原始订单记录</NDivider>
        <NDataTable :columns="debugOrderColumns" :data="debugData.orders || []" :pagination="{ pageSize: 5 }"
          max-height="300" />
      </div>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NPopconfirm,
  NSelect,
  NTag,
  NDatePicker,
  NAlert,
  NModal,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  NDataTable,
  NInputNumber,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon, getToken } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: '日成本核算单管理' })

const $message = useMessage()
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 详情模态框相关
const showDetailModal = ref(false)
const currentReport = ref(null)

// 调试模态框相关
const showDebugModal = ref(false)
const debugLoading = ref(false)
const debugData = ref(null)
const debugForm = ref({
  debugDate: null,
  recipeId: null
})
const debugFormRef = ref(null)

// 日期检查相关
const dateCheckMessage = ref('')
const dateCheckType = ref('info')
const recipeOptions = ref([])
const recipeLoading = ref(false)

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '已确认', value: 'confirmed' }
]

// 获取餐次类型名称
const getMealTypeName = (mealType) => {
  const mealTypes = {
    1: '早餐',
    2: '午餐',
    3: '晚餐'
  }
  return mealTypes[mealType] || `餐次${mealType}`
}

function formatDateForAPI(timestamp) {
  if (!timestamp) return null
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 使用CRUD composable
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '日成本核算单',
  initForm: {
    reportDate: null,
    recipeId: null
  },
  doCreate: async (data) => {
    const params = {
      report_date: data.reportDate,
      recipe_id: data.recipeId || null
    }
    return await api.generateDailyCostReport(params)
  },
  doDelete: (data) => api.deleteDailyCostReport({ id: data.id }),
  refresh: () => $table.value?.handleSearch(),
})

// 日期检查方法
const checkDateAvailable = async (value) => {
  if (!value) {
    dateCheckMessage.value = ''
    return
  }

  try {
    const response = await api.checkDateAvailable({ report_date: formatDateForAPI(value) })

    if (response.code === 200) {
      const data = response.data
      if (data.available) {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'success'
        if (data.recipe_id) {
          modalForm.value.recipeId = data.recipe_id
          recipeOptions.value = [{ label: data.recipe_name, value: data.recipe_id }]
        }
      } else {
        dateCheckMessage.value = data.message
        dateCheckType.value = 'warning'
      }
      // 手动触发表单验证
      modalFormRef.value?.validate(['reportDate'])
    }
  } catch (error) {
    dateCheckMessage.value = '检查日期失败'
    dateCheckType.value = 'error'
  }
}

// 表单验证规则
const generateRules = {
  reportDate: [
    { 
      required: true, 
      message: '请选择核算日期', 
      trigger: ['change', 'blur'],
      validator: (_, value) => {
        return !!value
      }
    }
  ]
}

// 表格列定义
const columns = [
  {
    title: '核算日期',
    key: 'report_date',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '食谱名称',
    key: 'recipe_name',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.status === 'confirmed' ? 'success' : 'warning' },
        { default: () => row.status === 'confirmed' ? '已确认' : '草稿' }
      )
    }
  },
  {
    title: '总成本',
    key: 'total_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.total_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '库存消耗',
    key: 'inventory_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.inventory_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '订单消耗',
    key: 'order_cost',
    width: 120,
    align: 'center',
    render(row) {
      return `¥${row.order_cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.created_at))
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => handleViewDetail(row),
            },
            {
              default: () => '查看详情',
              icon: renderIcon('material-symbols:visibility-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/daily-cost-report/get']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'warning',
              style: 'margin-right: 8px;',
              onClick: () => handleExport(row),
            },
            {
              default: () => '导出',
              icon: renderIcon('material-symbols:download', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/daily-cost-report/export']]
        ),
        row.status === 'draft' ? withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'success',
              style: 'margin-right: 8px;',
              onClick: () => handleConfirm(row),
            },
            {
              default: () => '确认',
              icon: renderIcon('material-symbols:check', { size: 16 }),
            }
          ),
          [[vPermission, 'post/api/v1/daily-cost-report/confirm']]
        ) : null,
        row.status === 'draft' ? h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/daily-cost-report/delete']]
              ),
            default: () => h('div', {}, '确定删除该核算单吗?'),
          }
        ) : null
      ]
    }
  }
]

// 详情表格列定义
const detailColumns = [
  { title: '食材名称', key: 'food_stuff_name', width: 150 },
  { title: '单位', key: 'unit_name', width: 80 },
  {
    title: '单价',
    key: 'price',
    width: 100,
    align: 'center',
    render: (row) => {
      // 优先使用 price 字段，如果不存在则从 consume_details 中提取
      let price = row.price
      if (!price && row.consume_details && row.consume_details.length > 0) {
        price = row.consume_details[0].price
      }
      return `¥${price?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '总消耗量',
    key: 'total_quantity',
    width: 120,
    align: 'center',
    render: (row) => {
      return `${row.total_quantity || 0}${row.unit_name}`
    }
  },
  {
    title: '库存消耗',
    key: 'inventory_consume',
    width: 180,
    render: (row) => {
      const consume = row.inventory_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '订单消耗',
    key: 'order_consume',
    width: 180,
    render: (row) => {
      const consume = row.order_consume
      if (!consume || consume.quantity === 0) return '-'
      return `${consume.quantity}${row.unit_name} / ¥${consume.cost?.toFixed(2) || '0.00'}`
    }
  },
  {
    title: '总消耗成本',
    key: 'total_cost',
    width: 120,
    align: 'center',
    render: (row) => {
      const inventoryCost = row.inventory_consume?.cost || 0
      const orderCost = row.order_consume?.cost || 0
      return `¥${(inventoryCost + orderCost).toFixed(2)}`
    }
  }
]

// 调试表格列定义
const debugStoreColumns = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '操作类型', key: 'operation_type', width: 150 },
  { title: '创建时间', key: 'created_at', width: 180 },
  {
    title: '食材数据',
    key: 'food_stuff_data',
    width: 300,
    render: (row) => {
      return JSON.stringify(row.food_stuff_data, null, 2)
    }
  }
]

const debugOrderColumns = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '订单号', key: 'order_number', width: 150 },
  { title: '状态', key: 'order_status', width: 100 },
  { title: '确认时间', key: 'order_confirm_date', width: 180 },
  {
    title: '订单项',
    key: 'order_items',
    width: 300,
    render: (row) => {
      return JSON.stringify(row.order_items, null, 2)
    }
  }
]

const debugOrderAnalysisColumns = [
  { title: '订单ID', key: 'order_id', width: 80 },
  { title: '订单号', key: 'order_number', width: 150 },
  { title: '状态', key: 'order_status', width: 100 },
  { title: '确认时间', key: 'order_confirm_date', width: 180 },
  {
    title: '消耗分析',
    key: 'items_analysis',
    width: 400,
    render: (row) => {
      const items = row.items_analysis || []
      const matchingItems = items.filter(item => item.matches_recipe || item.has_immediate_consume)
      const totalItems = items.length
      const consumeItems = matchingItems.length

      return h('div', [
        h('div', `总项目: ${totalItems}, 可消耗: ${consumeItems}`),
        ...matchingItems.map(item =>
          h('div', { style: 'font-size: 12px; color: #666; margin-top: 4px;' }, [
            `食材${item.food_stuff_id}: `,
            item.matches_recipe ? '✓食谱匹配 ' : '',
            item.has_immediate_consume ? '✓立即消耗 ' : '',
            item.has_price ? `价格${item.price} ` : '❌无价格 ',
            `数量${item.consume_quantity || item.quantity || 0}`
          ])
        )
      ])
    }
  }
]

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await api.getDailyCostReportById({ id: row.id })
    if (response.code === 200) {
      currentReport.value = response.data
      showDetailModal.value = true
    }
  } catch (error) {
    $message.error('获取详情失败')
  }
}

// 导出核算单
const handleExport = async (row) => {
  try {
    const token = getToken()
    const response = await fetch(`/api/v1/daily-cost-report/export?id=${row.id}`, {
      method: 'GET',
      headers: {
        'token': token,
      },
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `日成本核算表_${row.report_date}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      $message.success('导出成功')
    } else {
      const errorData = await response.json()
      $message.error(errorData.msg || '导出失败')
    }
  } catch (error) {
    $message.error('导出失败')
  }
}

// 确认核算单
const handleConfirm = async (row) => {
  try {
    const response = await api.confirmDailyCostReport({ id: row.id })
    if (response.code === 200) {
      $message.success('确认成功')
      $table.value?.handleSearch()
    } else {
      $message.error(response.msg || '确认失败')
    }
  } catch (error) {
    $message.error('确认失败')
  }
}

// 调试数据查看
const handleDebugData = async () => {
  if (!debugForm.value.debugDate) {
    $message.error('请选择调试日期')
    return
  }

  debugLoading.value = true
  try {
    const params = {
      report_date: debugForm.value.debugDate,
      recipe_id: debugForm.value.recipeId || undefined
    }

    const response = await api.debugDailyData(params)
    if (response.code === 200) {
      debugData.value = response.data
      $message.success('调试数据获取成功')
    } else {
      $message.error(response.msg || '获取调试数据失败')
    }
  } catch (error) {
    $message.error('获取调试数据失败')
  } finally {
    debugLoading.value = false
  }
}

// 初始化数据
onMounted(() => {
  $table.value?.handleSearch()
})
</script>


