from fastapi import APIRouter, Query, Body, Depends
from datetime import datetime

from app.controllers.daily_cost_report import daily_cost_report_controller
from app.schemas import Success, SuccessExtra, Fail
from app.schemas.daily_cost_report import DailyCostReportGenerate, DailyCostReportSearch

router = APIRouter()


@router.get("/list", summary="查询日成本核算单列表")
async def list_daily_cost_report(
    search: DailyCostReportSearch = Depends(),
):
    """获取日成本核算单列表"""
    try:
        result = await daily_cost_report_controller.get_report_list(search)
        return SuccessExtra(
            data=result["data"],
            total=result["total"],
            page=search.current,
            page_size=search.size
        )
    except Exception as e:
        return Fail(msg=f"获取核算单列表失败: {str(e)}")


@router.get("/get", summary="获取日成本核算单详情")
async def get_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """获取日成本核算单详情"""
    try:
        report = await daily_cost_report_controller.get_report_detail(id)
        return Success(data=report)
    except Exception as e:
        return Fail(msg=f"获取核算单详情失败: {str(e)}")


@router.post("/generate", summary="生成日成本核算单")
async def generate_daily_cost_report(
    generate_data: DailyCostReportGenerate = Body(...),
):
    """生成日成本核算单"""
    try:
        report = await daily_cost_report_controller.generate_report(generate_data)
        return Success(data={"id": report.id}, msg="生成核算单成功")
    except Exception as e:
        return Fail(msg=f"生成核算单失败: {str(e)}")


@router.post("/confirm", summary="确认日成本核算单")
async def confirm_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """确认日成本核算单"""
    try:
        await daily_cost_report_controller.confirm_report(id)
        return Success(msg="确认核算单成功")
    except Exception as e:
        return Fail(msg=f"确认核算单失败: {str(e)}")


@router.delete("/delete", summary="删除日成本核算单")
async def delete_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """删除日成本核算单"""
    try:
        await daily_cost_report_controller.delete_report(id)
        return Success(msg="删除核算单成功")
    except Exception as e:
        return Fail(msg=f"删除核算单失败: {str(e)}")


@router.get("/check-date", summary="检查指定日期是否可以生成核算单")
async def check_date_available(
    report_date: str = Query(..., description="核算日期", example="2025-03-11"),
):
    """检查指定日期是否可以生成核算单"""
    try:
        # 解析日期
        parsed_date = datetime.strptime(report_date, "%Y-%m-%d").date()
        
        # 检查是否已存在核算单
        from app.controllers.utils import get_school_id
        school_id = await get_school_id()
        
        existing_report = await daily_cost_report_controller.model.filter(
            report_date=parsed_date,
            school_id=school_id
        ).first()
        
        if existing_report:
            return Success(data={
                "available": False,
                "message": f"日期 {report_date} 的核算单已存在",
                "existing_report_id": existing_report.id
            })
        
        # 检查是否有食谱
        from app.models.food_menu import Recipe
        recipe = await Recipe.filter(
            recipe_date=parsed_date,
            school_id=school_id,
            is_active=True
        ).first()
        
        if not recipe:
            return Success(data={
                "available": False,
                "message": f"日期 {report_date} 没有可用的食谱"
            })
        
        return Success(data={
            "available": True,
            "message": f"日期 {report_date} 可以生成核算单",
            "recipe_id": recipe.id,
            "recipe_name": recipe.recipe_name
        })
        
    except ValueError:
        return Fail(msg="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        return Fail(msg=f"检查日期失败: {str(e)}")


@router.get("/export", summary="导出日成本核算单")
async def export_daily_cost_report(
    id: int = Query(..., description="核算单ID"),
):
    """导出日成本核算单为Excel格式"""
    try:
        from fastapi.responses import StreamingResponse
        from io import BytesIO
        import openpyxl
        from openpyxl.styles import Font, Alignment, Border, Side

        from app.controllers.utils import get_school_id
        from app.models.admin import Dept

        # 获取核算单详情
        report = await daily_cost_report_controller.get_report_detail(id)

        # 获取学校信息
        school_id = await get_school_id()
        school = await Dept.get_or_none(id=school_id)
        school_name = school.name if school else "未知学校"

        # 获取学生总数
        from app.controllers.dept import dept_controller
        total_students = await dept_controller.get_total_student_count()

        # 创建工作簿和工作表
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "日成本核算表"

        # 设置列宽
        ws.column_dimensions['A'].width = 10  # 饭菜名称/序号
        ws.column_dimensions['B'].width = 12  # 餐次名称/食材名称
        ws.column_dimensions['C'].width = 15  # 菜品名称/单位
        ws.column_dimensions['D'].width = 10  # 菜品名称/单价
        ws.column_dimensions['E'].width = 10  # 就餐人数/数量
        ws.column_dimensions['F'].width = 10  # 陪餐人数/金额
        ws.column_dimensions['G'].width = 8   # 幼儿人数/备注
        ws.column_dimensions['H'].width = 8   # 幼儿人数

        # 定义样式
        title_font = Font(name='宋体', size=16, bold=True)
        header_font = Font(name='宋体', size=12, bold=True)
        content_font = Font(name='宋体', size=10)
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        center_alignment = Alignment(horizontal='center', vertical='center')

        # 标题行
        ws.merge_cells('A1:H1')
        ws['A1'] = '"校园餐"日成本核算表'
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment

        # 基本信息行
        ws.merge_cells('A2:B2')
        ws['A2'] = f'单位：{school_name}'
        ws['A2'].font = content_font

        ws.merge_cells('E2:H2')
        report_date_str = report['report_date']
        ws['E2'] = f'时间：{report_date_str}'
        ws['E2'].font = content_font

        # 餐次信息行 - 动态布局
        row = 3

        # 餐次类型映射
        meal_type_names = {
            1: '早餐',
            2: '早点',
            3: '午餐',
            4: '午点',
            5: '晚餐'
        }

        if report.get('meals'):
            # 获取并排序餐次信息
            meals = report['meals']
            # 按meal_type排序
            meals.sort(key=lambda x: x.get('meal_type', 0))

            # 添加"饭菜名称"标题
            ws.merge_cells(f'A{row}:A{row + len(meals) - 1}')
            ws[f'A{row}'] = '饭菜名称'
            ws[f'A{row}'].font = content_font
            ws[f'A{row}'].alignment = center_alignment

            # 添加表头
            ws[f'E{row}'] = '就餐人数'
            ws[f'E{row}'].font = content_font
            ws[f'E{row}'].alignment = center_alignment

            ws[f'F{row}'] = '陪餐人数'
            ws[f'F{row}'].font = content_font
            ws[f'F{row}'].alignment = center_alignment

            ws.merge_cells(f'G{row}:H{row}')
            ws[f'G{row}'] = '幼儿人数'
            ws[f'G{row}'].font = content_font
            ws[f'G{row}'].alignment = center_alignment

            # 处理每个餐次
            for i, meal in enumerate(meals):
                current_row = row + i
                meal_type = meal.get('meal_type', 0)
                meal_name = meal_type_names.get(meal_type, f'餐次{meal_type}')

                # 获取菜品名称
                dish_list = meal.get('dish_list', [])
                dish_names = []
                for dish in dish_list:
                    if isinstance(dish, dict):
                        dish_names.append(dish.get('dish_name', ''))
                    else:
                        dish_names.append(str(dish))

                # 餐次名称 (B列)
                ws[f'B{current_row}'] = meal_name
                ws[f'B{current_row}'].font = content_font
                ws[f'B{current_row}'].alignment = center_alignment

                # 菜品名称 (C-D列合并)
                ws.merge_cells(f'C{current_row}:D{current_row}')
                ws[f'C{current_row}'] = ' '.join(dish_names) if dish_names else ''
                ws[f'C{current_row}'].font = content_font

                # 就餐人数 (E列)
                ws[f'E{current_row}'] = total_students
                ws[f'E{current_row}'].font = content_font
                ws[f'E{current_row}'].alignment = center_alignment

                # 陪餐人数 (F列) - 暂时设为3
                ws[f'F{current_row}'] = 3
                ws[f'F{current_row}'].font = content_font
                ws[f'F{current_row}'].alignment = center_alignment

                # 幼儿人数 (G-H列合并)
                ws.merge_cells(f'G{current_row}:H{current_row}')
                ws[f'G{current_row}'] = total_students
                ws[f'G{current_row}'].font = content_font
                ws[f'G{current_row}'].alignment = center_alignment

            row += len(meals)
        else:
            # 如果没有餐次信息，显示默认行
            ws.merge_cells(f'A{row}:A{row}')
            ws[f'A{row}'] = '饭菜名称'
            ws[f'A{row}'].font = content_font
            ws[f'A{row}'].alignment = center_alignment

            ws[f'B{row}'] = '餐次'
            ws[f'B{row}'].font = content_font
            ws[f'B{row}'].alignment = center_alignment

            ws.merge_cells(f'C{row}:D{row}')
            ws[f'C{row}'] = '暂无餐次信息'
            ws[f'C{row}'].font = content_font

            ws[f'E{row}'] = total_students
            ws[f'E{row}'].font = content_font
            ws[f'E{row}'].alignment = center_alignment

            ws[f'F{row}'] = 3
            ws[f'F{row}'].font = content_font
            ws[f'F{row}'].alignment = center_alignment

            ws.merge_cells(f'G{row}:H{row}')
            ws[f'G{row}'] = total_students
            ws[f'G{row}'].font = content_font
            ws[f'G{row}'].alignment = center_alignment

            row += 1

        # 表头
        headers = ['序号', '食材名称', '单位、规格', '单价(元)', '数量', '金额', '备注', '']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = border

        row += 1

        # 按食材类型分组显示成本明细
        cost_details = report.get('cost_details', [])

        # 分类食材
        bulk_commodities = []  # 大宗商品
        raw_materials = []     # 原辅材料
        loose_foods = []       # 散货食材

        for detail in cost_details:
            food_stuff_id = detail.get('food_stuff_id')
            if food_stuff_id:
                # 获取食材类型
                from app.models.food_stuff import FoodStuff
                food_stuff = await FoodStuff.get_or_none(id=food_stuff_id)
                if food_stuff:
                    if food_stuff.food_stuff_type == 1:  # 大宗商品
                        bulk_commodities.append(detail)
                    elif food_stuff.food_stuff_type == 2:  # 原辅材料
                        raw_materials.append(detail)
                    elif food_stuff.food_stuff_type == 3:  # 散货食材
                        loose_foods.append(detail)
                else:
                    # 如果找不到食材信息，默认归类为散货食材
                    loose_foods.append(detail)

        # 添加分类标题和数据
        categories = [
            ('1.大宗商品', bulk_commodities),
            ('2.原辅材料', raw_materials),
            ('3.散货食材', loose_foods)
        ]

        item_index = 1
        total_amount = 0.0

        for category_name, items in categories:
            if items:  # 只显示有数据的分类
                # 分类标题
                ws.merge_cells(f'A{row}:H{row}')
                ws[f'A{row}'] = category_name
                ws[f'A{row}'].font = header_font
                ws[f'A{row}'].alignment = Alignment(horizontal='left', vertical='center')
                ws[f'A{row}'].border = border
                row += 1

                # 分类下的食材明细
                for detail in items:
                    food_name = detail.get('food_stuff_name', '')
                    unit_name = detail.get('unit_name', '')
                    quantity = detail.get('total_quantity', 0.0)
                    amount = detail.get('total_cost', 0.0)

                    # 从consume_details中提取价格信息
                    price = 0.0
                    consume_details = detail.get('consume_details', [])
                    if consume_details:
                        # 使用第一个消耗记录的价格
                        price = consume_details[0].get('price', 0.0)
                    else:
                        # 如果没有consume_details，尝试从order_consume中获取
                        order_consume = detail.get('order_consume', {})
                        orders = order_consume.get('orders', [])
                        if orders:
                            price = orders[0].get('price', 0.0)
                        else:
                            # 最后尝试从inventory_consume中获取
                            inventory_consume = detail.get('inventory_consume', {})
                            orders = inventory_consume.get('orders', [])
                            if orders:
                                price = orders[0].get('price', 0.0)

                    print(f"=== 导出处理食材: {food_name} ===")
                    print(f"提取的价格: {price}, 数量: {quantity}, 金额: {amount}")
                    print("=== 食材处理结束 ===")

                    # 数据行
                    ws[f'A{row}'] = item_index
                    ws[f'B{row}'] = food_name
                    ws[f'C{row}'] = unit_name
                    ws[f'D{row}'] = f'{price:.2f}' if price is not None else '0.00'
                    ws[f'E{row}'] = f'{quantity:.2f}' if quantity is not None else '0.00'
                    ws[f'F{row}'] = f'{amount:.2f}' if amount is not None else '0.00'
                    ws[f'G{row}'] = ''  # 备注列暂时为空
                    ws[f'H{row}'] = ''  # 第8列暂时为空

                    # 设置样式
                    for col in range(1, 9):  # 改为9列
                        cell = ws.cell(row=row, column=col)
                        cell.font = content_font
                        cell.border = border
                        if col in [1, 4, 5, 6]:  # 序号、单价、数量、金额列居中
                            cell.alignment = center_alignment

                    total_amount += amount
                    item_index += 1
                    row += 1

        # 合计行
        ws.merge_cells(f'A{row}:E{row}')
        ws[f'A{row}'] = '合计支出金额'
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].alignment = center_alignment
        ws[f'A{row}'].border = border

        ws[f'F{row}'] = f'{total_amount:.2f}'
        ws[f'F{row}'].font = header_font
        ws[f'F{row}'].alignment = center_alignment
        ws[f'F{row}'].border = border

        ws[f'G{row}'] = ''
        ws[f'G{row}'].border = border

        ws[f'H{row}'] = ''
        ws[f'H{row}'].border = border

        row += 1

        # 核算成本行
        ws.merge_cells(f'A{row}:B{row}')
        ws[f'A{row}'] = '核算成本、单价(元/人)'
        ws[f'A{row}'].font = content_font
        ws[f'A{row}'].alignment = center_alignment
        ws[f'A{row}'].border = border

        ws[f'C{row}'] = '用餐幼儿'
        ws[f'C{row}'].font = content_font
        ws[f'C{row}'].alignment = center_alignment
        ws[f'C{row}'].border = border

        # 计算幼儿餐费
        child_cost_per_person = total_amount / total_students if total_students > 0 else 0
        ws[f'D{row}'] = f'当天餐费: {child_cost_per_person:.2f} 元'
        ws[f'D{row}'].font = content_font
        ws[f'D{row}'].alignment = center_alignment
        ws[f'D{row}'].border = border

        ws.merge_cells(f'E{row}:F{row}')
        ws[f'E{row}'] = f'合计: {total_amount:.2f}元'
        ws[f'E{row}'].font = content_font
        ws[f'E{row}'].alignment = center_alignment
        ws[f'E{row}'].border = border

        ws.merge_cells(f'G{row}:H{row}')
        ws[f'G{row}'] = ''
        ws[f'G{row}'].border = border

        row += 1

        ws[f'C{row}'] = '陪餐人'
        ws[f'C{row}'].font = content_font
        ws[f'C{row}'].alignment = center_alignment
        ws[f'C{row}'].border = border

        # 陪餐人餐费（暂时空着）
        ws[f'D{row}'] = '当天餐费: 元'
        ws[f'D{row}'].font = content_font
        ws[f'D{row}'].alignment = center_alignment
        ws[f'D{row}'].border = border

        ws.merge_cells(f'E{row}:F{row}')
        ws[f'E{row}'] = '合计: 元'
        ws[f'E{row}'].font = content_font
        ws[f'E{row}'].alignment = center_alignment
        ws[f'E{row}'].border = border

        ws.merge_cells(f'G{row}:H{row}')
        ws[f'G{row}'] = ''
        ws[f'G{row}'].border = border

        # 空行
        row += 2

        # 签名行
        ws[f'A{row}'] = '厨师长:'
        ws[f'A{row}'].font = content_font

        ws[f'C{row}'] = '核算人:'
        ws[f'C{row}'].font = content_font

        ws[f'E{row}'] = '审核人:'
        ws[f'E{row}'].font = content_font

        ws[f'G{row}'] = '负责人:'
        ws[f'G{row}'].font = content_font

        # 保存到内存
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # 生成文件名
        from urllib.parse import quote
        filename = f"日成本核算表_{report_date_str}.xlsx"
        encoded_filename = quote(filename.encode('utf-8'))

        # 返回文件流
        return StreamingResponse(
            BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )

    except Exception as e:
        return Fail(msg=f"导出核算单失败: {str(e)}")


@router.get("/debug-data", summary="调试数据查看")
async def debug_daily_data(
    report_date: str = Query(..., description="核算日期", example="2025-03-11"),
    recipe_id: int = Query(None, description="食谱ID"),
):
    """调试接口：查看指定日期的相关数据"""
    try:
        from datetime import datetime
        from app.controllers.utils import get_school_id
        from app.models.food_stuff import FoodStuffStoreRecord
        from app.models.orders import Order

        # 解析日期
        parsed_date = datetime.strptime(report_date, "%Y-%m-%d").date()
        school_id = await get_school_id()

        start_of_day = datetime.combine(parsed_date, datetime.min.time())
        end_of_day = datetime.combine(parsed_date, datetime.max.time())

        # 获取当日所有库存记录
        all_store_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            created_at__gte=start_of_day,
            created_at__lte=end_of_day
        ).all()

        # 获取当日所有订单
        all_orders = await Order.filter(
            school_id=school_id,
            order_confirm_date__gte=start_of_day,
            order_confirm_date__lte=end_of_day
        ).all()

        # 整理数据
        store_records_data = []
        for record in all_store_records:
            store_records_data.append({
                "id": record.id,
                "operation_type": record.operation_type,
                "food_stuff_data": record.food_stuff_data,
                "created_at": record.created_at.isoformat()
            })

        orders_data = []
        for order in all_orders:
            orders_data.append({
                "id": order.id,
                "order_number": order.order_number,
                "order_status": order.order_status,
                "order_items": order.order_items,
                "order_confirm_date": order.order_confirm_date.isoformat() if order.order_confirm_date else None
            })

        # 分析订单消耗情况
        order_analysis = []
        for order in all_orders:
            analysis = {
                "order_id": order.id,
                "order_number": order.order_number,
                "order_status": order.order_status,
                "order_confirm_date": order.order_confirm_date.isoformat() if order.order_confirm_date else None,
                "created_at": order.created_at.isoformat(),
                "items_analysis": []
            }

            for item in order.order_items:
                item_analysis = {
                    "food_stuff_id": item.get('id'),
                    "quantity": item.get('quantity'),
                    "consume_quantity": item.get('consume_quantity'),
                    "price": item.get('price'),
                    "recipe_id": item.get('recipe_id'),
                    "immediate_consume": item.get('immediate_consume'),
                    "matches_recipe": item.get('recipe_id') == recipe_id,
                    "has_immediate_consume": bool(item.get('immediate_consume')),
                    "has_price": bool(item.get('price'))
                }
                analysis["items_analysis"].append(item_analysis)

            order_analysis.append(analysis)

        return Success(data={
            "date": report_date,
            "school_id": school_id,
            "recipe_id": recipe_id,
            "store_records": store_records_data,
            "orders": orders_data,
            "order_analysis": order_analysis,
            "store_records_count": len(store_records_data),
            "orders_count": len(orders_data),
            "summary": {
                "total_store_records": len(store_records_data),
                "total_orders": len(orders_data),
                "confirmed_orders": len([o for o in all_orders if o.order_status == "confirmed"]),
                "orders_with_recipe_id": len([o for o in all_orders if any(item.get('recipe_id') == recipe_id for item in o.order_items)]),
                "orders_with_immediate_consume": len([o for o in all_orders if any(item.get('immediate_consume') for item in o.order_items)])
            }
        }, msg="调试数据获取成功")

    except Exception as e:
        return Fail(msg=f"获取调试数据失败: {str(e)}")
