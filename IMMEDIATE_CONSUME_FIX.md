# 入库立即消耗记录修复 - 添加价格和食谱ID信息

## 问题描述

在入库时立即消耗的库存变化记录中，缺少以下关键信息：
1. **consume_order_id**: 消耗订单ID（当前订单的ID）
2. **consume_recipe_id**: 消耗食谱ID（当前订单的食谱ID）
3. **consume_price**: 消耗订单的价格信息

这导致成本核算时无法准确区分：
- 普通库存消耗（从已有库存中扣除）
- 订单消耗（入库后立即消耗）

## 根本原因

### 1. 缺少消耗上下文信息
- **文件**: `app/controllers/food_stuff_store.py` 的 `confirm_receive` 方法
- **问题**: 在处理立即消耗时，只记录了被扣除库存的信息，没有记录当前订单的上下文
- **影响**: 无法区分是哪个订单/食谱触发的消耗

### 2. 成本核算逻辑不完善
- **文件**: `app/controllers/daily_cost_report.py`
- **问题**: 无法正确识别和分类立即消耗的记录
- **影响**: 立即消耗可能被错误归类为普通库存消耗

## 修复方案

### 1. 增强立即消耗记录

**修改位置**: `app/controllers/food_stuff_store.py` 第377-396行

**核心改进**:
```python
# 收集立即消耗的变化信息，并添加当前订单的上下文信息
for change in deduction_result['inventory_changes']:
    # 为每个消耗记录添加当前订单的上下文信息
    enhanced_change = {
        **change,
        "consume_order_id": order.id,  # 消耗订单ID
        "consume_recipe_id": recipe_id,  # 消耗食谱ID
        "consume_price": float(price) if price else 0.0  # 消耗订单的价格
    }
    immediate_consume_changes.append(enhanced_change)
```

**新增字段说明**:
- `consume_order_id`: 触发消耗的订单ID（当前入库订单）
- `consume_recipe_id`: 触发消耗的食谱ID（当前订单的食谱）
- `consume_price`: 触发消耗的订单价格（当前订单的价格）

### 2. 增强记录存储方法

**修改位置**: `app/controllers/food_stuff_store.py` 第231-247行

**新增字段处理**:
```python
food_item = {
    # ... 原有字段 ...
    # 立即消耗的额外信息
    "consume_order_id": change.get("consume_order_id"),  # 消耗订单ID
    "consume_recipe_id": change.get("consume_recipe_id"),  # 消耗食谱ID
    "consume_price": change.get("consume_price", 0.0)  # 消耗订单价格
}
```

### 3. 传递食谱ID信息

**修改位置**: `app/controllers/food_stuff_store.py` 第412-427行

**改进逻辑**:
```python
# 记录立即消耗的库存变化
if immediate_consume_changes:
    # 获取当前订单的recipe_id（如果有的话）
    current_recipe_id = None
    if order.order_items:
        # 从第一个订单项中获取recipe_id
        current_recipe_id = order.order_items[0].get("recipe_id")
    
    await self.record_store_change(
        operation_type="下单库存消耗",
        remark=f"订单{order.order_number}入库后立即消耗",
        food_stuff_changes=immediate_consume_changes,
        school_id=user_dept.id,
        order_id=order.id,
        recipe_id=current_recipe_id  # 传递食谱ID
    )
```

### 4. 优化成本核算逻辑

**修改位置**: `app/controllers/daily_cost_report.py` 第231-269行

**智能分类逻辑**:
```python
# 检查是否是立即消耗（有consume_order_id字段）
consume_order_id = food_data.get('consume_order_id')
consume_recipe_id = food_data.get('consume_recipe_id')
consume_price = food_data.get('consume_price', 0.0)

if consume_order_id:
    # 这是入库后立即消耗，归类为订单消耗
    # 使用消耗订单的价格（如果有的话）
    if consume_price > 0:
        cost = consume_quantity * consume_price
    
    food_stuff_costs[food_stuff_id]['order_consume']['quantity'] += consume_quantity
    food_stuff_costs[food_stuff_id]['order_consume']['cost'] += cost
    food_stuff_costs[food_stuff_id]['order_consume']['orders'].append({
        'order_id': consume_order_id,
        'quantity': consume_quantity,
        'price': consume_price if consume_price > 0 else price,
        'recipe_id': consume_recipe_id,
        'source_order_id': order_id,  # 被消耗库存的来源订单
        'source_recipe_id': recipe_id_from_data  # 被消耗库存的来源食谱
    })
else:
    # 普通库存消耗
    # ... 原有逻辑 ...
```

## 数据结构说明

### 立即消耗记录的完整数据结构

```json
{
  "food_stuff_id": 1,
  "food_stuff_name": "大米",
  "before_count": 100,
  "change_count": -50,
  "after_count": 50,
  "unit_name": "公斤",
  
  // 被消耗库存的信息
  "price": 5.0,           // 被消耗库存的价格
  "order_id": 10,         // 被消耗库存的来源订单ID
  "recipe_id": 2,         // 被消耗库存的来源食谱ID
  
  // 消耗触发者的信息
  "consume_order_id": 15, // 触发消耗的订单ID
  "consume_recipe_id": 3, // 触发消耗的食谱ID
  "consume_price": 6.0,   // 触发消耗的订单价格
  
  // 列表信息（如果有多个来源）
  "order_ids": [10],      // 所有相关订单ID
  "recipe_ids": [2]       // 所有相关食谱ID
}
```

## 成本核算改进

### 1. 准确分类
- **订单消耗**: 有 `consume_order_id` 的记录
- **库存消耗**: 没有 `consume_order_id` 的记录

### 2. 价格优先级
1. **消耗价格**: `consume_price`（触发消耗的订单价格）
2. **库存价格**: `price`（被消耗库存的价格）
3. **平均价格**: 系统计算的平均价格

### 3. 完整追溯
- **消耗方**: `consume_order_id`, `consume_recipe_id`
- **被消耗方**: `order_id`, `recipe_id`
- **支持复杂的成本分析和审计**

## 测试验证

### 测试脚本
- **文件**: `test_immediate_consume.py`
- **功能**: 验证立即消耗记录的数据完整性
- **检查项**: 价格、订单ID、食谱ID等关键字段

### 验证要点
1. **数据完整性**: 所有关键字段都被正确记录
2. **分类准确性**: 立即消耗被正确归类为订单消耗
3. **价格准确性**: 使用正确的价格进行成本计算
4. **追溯完整性**: 可以追溯到消耗的触发者和被消耗的来源

## 修复效果

### 1. 准确的成本分类
- 立即消耗正确归类为"订单消耗"
- 普通库存扣除归类为"库存消耗"
- 避免重复计算和错误分类

### 2. 完整的追溯信息
- 可以追溯消耗的触发订单和食谱
- 可以追溯被消耗库存的来源
- 支持复杂的成本分析需求

### 3. 准确的价格计算
- 优先使用消耗订单的价格
- 回退到被消耗库存的价格
- 最后使用系统平均价格

## 注意事项

1. **向后兼容**: 旧记录仍可正常处理，只是缺少新的字段
2. **数据一致性**: 确保所有立即消耗操作都使用新的记录格式
3. **性能影响**: 新增字段对性能影响很小

## 后续建议

1. **监控验证**: 监控新记录的数据完整性
2. **报表优化**: 基于新的分类逻辑优化成本报表
3. **历史数据**: 考虑为历史数据补充缺失的消耗上下文信息
