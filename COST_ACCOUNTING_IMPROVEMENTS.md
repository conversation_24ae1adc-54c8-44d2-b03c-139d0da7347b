# 成本核算系统改进 - 基于库存变化记录

## 概述

本次改进将成本核算系统从基于订单数据改为基于库存变化记录，使成本核算更加准确和可追溯。

## 主要修改

### 1. 数据库模型修改

#### FoodStuffStoreRecord 模型增强
- **文件**: `app/models/food_stuff.py`
- **新增字段**:
  - `order_id`: 关联订单ID (可为空)
  - `recipe_id`: 关联食谱ID (可为空)
- **food_stuff_data JSON格式扩展**:
  - 增加 `price`: 单价信息
  - 增加 `order_id`: 订单ID
  - 增加 `recipe_id`: 食谱ID

#### 数据库迁移
- **迁移文件**: `migrations/models/30_20250619151219_update.py`
- 已添加 `order_id` 和 `recipe_id` 字段到 `food_stuff_store_record` 表

### 2. Schema 更新

#### FoodStuffStoreRecordBase 扩展
- **文件**: `app/schemas/food_stuff_store.py`
- **新增字段**:
  - `order_id`: 关联订单ID
  - `recipe_id`: 关联食谱ID

### 3. Controller 修改

#### FoodStuffStoreController 增强
- **文件**: `app/controllers/food_stuff_store.py`
- **record_store_change 方法**:
  - 新增 `order_id` 和 `recipe_id` 参数
  - 在 food_stuff_data 中记录价格、订单ID、食谱ID信息
- **confirm_receive 方法**:
  - 在记录库存变化时传递 order_id 和 recipe_id
  - 入库和消耗记录都包含完整的追溯信息

#### OrderController 修改
- **文件**: `app/controllers/order.py`
- **_process_inventory_and_order 方法**:
  - 新增 `recipe_id` 参数
  - 在记录库存消耗时传递 recipe_id
- **create_order 方法**:
  - 调用时传递 recipe_id 参数

#### DailyCostReportController 重构
- **文件**: `app/controllers/daily_cost_report.py`
- **_calculate_daily_cost 方法完全重写**:
  - 主要基于库存变化记录计算成本
  - 支持按 recipe_id 过滤记录
  - 自动区分库存消耗和订单消耗
  - 从记录中获取价格信息，提高准确性

## 新的成本核算逻辑

### 数据来源
1. **主要数据源**: `FoodStuffStoreRecord` 库存变化记录
2. **辅助数据源**: 库存记录中的价格信息

### 成本分类
1. **库存消耗**: 
   - 操作类型为 "下单库存消耗"、"食谱库存消耗"、"库存消耗"
   - 变化量为负数（消耗）
2. **订单消耗**: 
   - 入库后立即消耗的情况
   - 通过订单ID关联入库和消耗记录

### 价格获取策略
1. **优先级1**: 库存变化记录中的价格信息
2. **优先级2**: 调用 `_get_food_stuff_avg_price` 获取平均价格
3. **优先级3**: 使用默认价格（库存消耗: 10.0, 订单消耗: 15.0）

## 优势

### 1. 数据准确性
- 直接基于实际的库存变化记录
- 避免了从订单推断消耗的复杂逻辑
- 价格信息更准确

### 2. 可追溯性
- 每条库存变化记录都包含 order_id 和 recipe_id
- 可以追溯到具体的订单和食谱
- 支持详细的成本分析

### 3. 简化逻辑
- 减少了复杂的条件判断
- 统一的数据处理流程
- 更容易维护和扩展

## 测试

### 测试脚本
- **文件**: `test_cost_accounting.py`
- 可以测试新的成本核算逻辑
- 显示详细的计算过程和结果

### API 调试接口
- **接口**: `/api/daily_cost_report/debug-data`
- 可以查看指定日期的所有相关数据
- 帮助调试和验证成本核算逻辑

## 使用方法

### 1. 生成成本核算单
```bash
POST /api/daily_cost_report/generate
{
  "report_date": "2025-06-19",
  "recipe_id": 1
}
```

### 2. 查看调试数据
```bash
GET /api/daily_cost_report/debug-data?report_date=2025-06-19&recipe_id=1
```

### 3. 运行测试脚本
```bash
python test_cost_accounting.py
```

## 注意事项

1. **数据库迁移**: 确保已应用最新的迁移文件
2. **历史数据**: 旧的库存变化记录可能缺少 order_id 和 recipe_id 信息
3. **价格信息**: 新的记录会包含完整的价格信息，提高成本核算准确性

## 后续改进建议

1. **批量数据修复**: 为历史数据补充缺失的 order_id 和 recipe_id
2. **报表优化**: 基于新的数据结构优化成本报表
3. **性能优化**: 对于大量数据的查询可以考虑添加索引
4. **前端适配**: 更新前端页面以显示更详细的成本追溯信息
