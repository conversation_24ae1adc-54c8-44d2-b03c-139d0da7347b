#!/usr/bin/env python3
"""
测试日成本核算表导出布局
"""

import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from io import BytesIO

def test_export_layout():
    """测试导出布局"""
    
    # 模拟数据
    school_name = "商州区第十二幼儿园"
    report_date_str = "2025-03-11"
    total_students = 720
    
    # 模拟餐次数据
    meals = [
        {
            'meal_type': 1,  # 早餐
            'dish_list': [
                {'dish_name': '八宝粥'},
                {'dish_name': '煮鸡蛋'}
            ]
        },
        {
            'meal_type': 3,  # 午餐
            'dish_list': [
                {'dish_name': '肉丝炒面'},
                {'dish_name': '紫菜汤'}
            ]
        },
        {
            'meal_type': 4,  # 午点
            'dish_list': [
                {'dish_name': '坚果'},
                {'dish_name': '小蛋糕'}
            ]
        }
    ]
    
    # 创建工作簿和工作表
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "日成本核算表"

    # 设置列宽
    ws.column_dimensions['A'].width = 10  # 饭菜名称/序号
    ws.column_dimensions['B'].width = 12  # 餐次名称/食材名称
    ws.column_dimensions['C'].width = 15  # 菜品名称/单位
    ws.column_dimensions['D'].width = 10  # 菜品名称/单价
    ws.column_dimensions['E'].width = 10  # 就餐人数/数量
    ws.column_dimensions['F'].width = 10  # 陪餐人数/金额
    ws.column_dimensions['G'].width = 8   # 幼儿人数/备注
    ws.column_dimensions['H'].width = 8   # 幼儿人数

    # 定义样式
    title_font = Font(name='宋体', size=16, bold=True)
    header_font = Font(name='宋体', size=12, bold=True)
    content_font = Font(name='宋体', size=10)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')

    # 标题行
    ws.merge_cells('A1:H1')
    ws['A1'] = '"校园餐"日成本核算表'
    ws['A1'].font = title_font
    ws['A1'].alignment = center_alignment

    # 基本信息行
    ws.merge_cells('A2:B2')
    ws['A2'] = f'单位：{school_name}'
    ws['A2'].font = content_font

    ws.merge_cells('E2:H2')
    ws['E2'] = f'时间：{report_date_str}'
    ws['E2'].font = content_font

    # 餐次信息行 - 动态布局
    row = 3
    
    # 餐次类型映射
    meal_type_names = {
        1: '早餐',
        2: '早点', 
        3: '午餐',
        4: '午点',
        5: '晚餐'
    }
    
    # 获取并排序餐次信息
    meals.sort(key=lambda x: x.get('meal_type', 0))
    
    # 添加"饭菜名称"标题
    ws.merge_cells(f'A{row}:A{row + len(meals) - 1}')
    ws[f'A{row}'] = '饭菜名称'
    ws[f'A{row}'].font = content_font
    ws[f'A{row}'].alignment = center_alignment
    
    # 添加表头
    ws[f'E{row}'] = '就餐人数'
    ws[f'E{row}'].font = content_font
    ws[f'E{row}'].alignment = center_alignment
    
    ws[f'F{row}'] = '陪餐人数'
    ws[f'F{row}'].font = content_font
    ws[f'F{row}'].alignment = center_alignment
    
    ws.merge_cells(f'G{row}:H{row}')
    ws[f'G{row}'] = '幼儿人数'
    ws[f'G{row}'].font = content_font
    ws[f'G{row}'].alignment = center_alignment
    
    # 处理每个餐次
    for i, meal in enumerate(meals):
        current_row = row + i
        meal_type = meal.get('meal_type', 0)
        meal_name = meal_type_names.get(meal_type, f'餐次{meal_type}')
        
        # 获取菜品名称
        dish_list = meal.get('dish_list', [])
        dish_names = []
        for dish in dish_list:
            if isinstance(dish, dict):
                dish_names.append(dish.get('dish_name', ''))
            else:
                dish_names.append(str(dish))
        
        # 餐次名称 (B列)
        ws[f'B{current_row}'] = meal_name
        ws[f'B{current_row}'].font = content_font
        ws[f'B{current_row}'].alignment = center_alignment
        
        # 菜品名称 (C-D列合并)
        ws.merge_cells(f'C{current_row}:D{current_row}')
        ws[f'C{current_row}'] = ' '.join(dish_names) if dish_names else ''
        ws[f'C{current_row}'].font = content_font
        
        # 就餐人数 (E列)
        ws[f'E{current_row}'] = total_students
        ws[f'E{current_row}'].font = content_font
        ws[f'E{current_row}'].alignment = center_alignment
        
        # 陪餐人数 (F列) - 暂时设为3
        ws[f'F{current_row}'] = 3
        ws[f'F{current_row}'].font = content_font
        ws[f'F{current_row}'].alignment = center_alignment
        
        # 幼儿人数 (G-H列合并)
        ws.merge_cells(f'G{current_row}:H{current_row}')
        ws[f'G{current_row}'] = total_students
        ws[f'G{current_row}'].font = content_font
        ws[f'G{current_row}'].alignment = center_alignment
    
    row += len(meals)

    # 表头
    headers = ['序号', '食材名称', '单位、规格', '单价(元)', '数量', '金额', '备注', '']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col, value=header)
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = border

    row += 1

    # 模拟一些食材数据
    food_items = [
        ('1.大宗商品', [
            {'name': '面粉', 'unit': '斤', 'price': 3.50, 'quantity': 10.0, 'amount': 35.0},
            {'name': '大米', 'unit': '斤', 'price': 4.20, 'quantity': 15.0, 'amount': 63.0}
        ]),
        ('2.原辅材料', [
            {'name': '食用油', 'unit': '升', 'price': 12.0, 'quantity': 2.0, 'amount': 24.0}
        ])
    ]

    item_index = 1
    total_amount = 0.0

    for category_name, items in food_items:
        # 分类标题
        ws.merge_cells(f'A{row}:H{row}')
        ws[f'A{row}'] = category_name
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].alignment = Alignment(horizontal='left', vertical='center')
        ws[f'A{row}'].border = border
        row += 1

        # 分类下的食材明细
        for item in items:
            # 数据行
            ws[f'A{row}'] = item_index
            ws[f'B{row}'] = item['name']
            ws[f'C{row}'] = item['unit']
            ws[f'D{row}'] = f"{item['price']:.2f}"
            ws[f'E{row}'] = f"{item['quantity']:.2f}"
            ws[f'F{row}'] = f"{item['amount']:.2f}"
            ws[f'G{row}'] = ''  # 备注列暂时为空
            ws[f'H{row}'] = ''  # 第8列暂时为空

            # 设置样式
            for col in range(1, 9):  # 9列
                cell = ws.cell(row=row, column=col)
                cell.font = content_font
                cell.border = border
                if col in [1, 4, 5, 6]:  # 序号、单价、数量、金额列居中
                    cell.alignment = center_alignment

            total_amount += item['amount']
            item_index += 1
            row += 1

    # 合计行
    ws.merge_cells(f'A{row}:E{row}')
    ws[f'A{row}'] = '合计支出金额'
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].alignment = center_alignment
    ws[f'A{row}'].border = border

    ws[f'F{row}'] = f'{total_amount:.2f}'
    ws[f'F{row}'].font = header_font
    ws[f'F{row}'].alignment = center_alignment
    ws[f'F{row}'].border = border

    ws[f'G{row}'] = ''
    ws[f'G{row}'].border = border
    
    ws[f'H{row}'] = ''
    ws[f'H{row}'].border = border

    row += 1

    # 核算成本行
    ws.merge_cells(f'A{row}:B{row}')
    ws[f'A{row}'] = '核算成本、单价(元/人)'
    ws[f'A{row}'].font = content_font
    ws[f'A{row}'].alignment = center_alignment
    ws[f'A{row}'].border = border

    ws[f'C{row}'] = '用餐幼儿'
    ws[f'C{row}'].font = content_font
    ws[f'C{row}'].alignment = center_alignment
    ws[f'C{row}'].border = border

    # 计算幼儿餐费
    child_cost_per_person = total_amount / total_students if total_students > 0 else 0
    ws[f'D{row}'] = f'当天餐费: {child_cost_per_person:.2f} 元'
    ws[f'D{row}'].font = content_font
    ws[f'D{row}'].alignment = center_alignment
    ws[f'D{row}'].border = border

    ws.merge_cells(f'E{row}:F{row}')
    ws[f'E{row}'] = f'合计: {total_amount:.2f}元'
    ws[f'E{row}'].font = content_font
    ws[f'E{row}'].alignment = center_alignment
    ws[f'E{row}'].border = border

    ws.merge_cells(f'G{row}:H{row}')
    ws[f'G{row}'] = ''
    ws[f'G{row}'].border = border

    row += 1

    ws[f'C{row}'] = '陪餐人'
    ws[f'C{row}'].font = content_font
    ws[f'C{row}'].alignment = center_alignment
    ws[f'C{row}'].border = border

    # 陪餐人餐费（暂时空着）
    ws[f'D{row}'] = '当天餐费: 元'
    ws[f'D{row}'].font = content_font
    ws[f'D{row}'].alignment = center_alignment
    ws[f'D{row}'].border = border

    ws.merge_cells(f'E{row}:F{row}')
    ws[f'E{row}'] = '合计: 元'
    ws[f'E{row}'].font = content_font
    ws[f'E{row}'].alignment = center_alignment
    ws[f'E{row}'].border = border

    ws.merge_cells(f'G{row}:H{row}')
    ws[f'G{row}'] = ''
    ws[f'G{row}'].border = border

    # 空行
    row += 2

    # 签名行
    ws[f'A{row}'] = '厨师长:'
    ws[f'A{row}'].font = content_font

    ws[f'C{row}'] = '核算人:'
    ws[f'C{row}'].font = content_font

    ws[f'E{row}'] = '审核人:'
    ws[f'E{row}'].font = content_font
    
    ws[f'G{row}'] = '负责人:'
    ws[f'G{row}'].font = content_font

    # 保存文件
    filename = f"测试_日成本核算表_{report_date_str}.xlsx"
    wb.save(filename)
    print(f"测试文件已保存: {filename}")
    
    # 打印布局信息
    print(f"\n布局信息:")
    print(f"- 餐次数量: {len(meals)}")
    print(f"- 饭菜名称占用行数: A3:A{3 + len(meals) - 1}")
    print(f"- 表头开始行: {3 + len(meals)}")
    print(f"- 总列数: 8 (A-H)")

if __name__ == "__main__":
    test_export_layout()
