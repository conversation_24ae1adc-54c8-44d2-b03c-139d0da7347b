#!/usr/bin/env python3
"""
测试基于 recipe_id 的成本核算查询逻辑
验证不依赖日期匹配的查询方式
"""

import asyncio
from datetime import date, datetime, timedelta
from tortoise import Tortoise

from app.models.food_stuff import FoodStuffStoreRecord
from app.controllers.daily_cost_report import daily_cost_report_controller


async def test_recipe_cost_query():
    """测试基于 recipe_id 的成本查询"""
    print("开始测试基于 recipe_id 的成本核算查询...")

    # 测试参数
    school_id = 1
    recipe_id = 1
    test_date = date.today()

    print(f"测试参数:")
    print(f"  学校ID: {school_id}")
    print(f"  食谱ID: {recipe_id}")
    print(f"  报告日期: {test_date}")

    try:
        # 1. 查看所有与该 recipe_id 相关的库存变化记录
        print("\n1. 查找与 recipe_id 相关的所有库存变化记录:")
        recipe_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            recipe_id=recipe_id
        ).all()
        
        print(f"找到 {len(recipe_records)} 条与食谱 {recipe_id} 相关的记录")
        
        for record in recipe_records:
            print(f"  记录ID: {record.id}")
            print(f"  操作类型: {record.operation_type}")
            print(f"  创建时间: {record.created_at}")
            print(f"  订单ID: {record.order_id}")
            print(f"  食谱ID: {record.recipe_id}")
            
            # 检查消耗记录
            consume_count = 0
            for food_data in record.food_stuff_data:
                change_count = food_data.get('change_count', 0)
                if change_count < 0:  # 消耗记录
                    consume_count += 1
                    print(f"    消耗: 食材ID {food_data.get('food_stuff_id')}, 数量 {abs(change_count)}")
            
            if consume_count == 0:
                print(f"    无消耗记录")
            print()

        # 2. 使用新的成本计算方法
        print("2. 使用优化后的成本计算方法:")
        cost_details = await daily_cost_report_controller._calculate_daily_cost(
            test_date, school_id, recipe_id
        )
        
        print(f"计算结果: 找到 {len(cost_details)} 种食材的成本数据")
        
        total_cost = 0.0
        for detail in cost_details:
            food_name = detail.get('food_stuff_name', '未知食材')
            quantity = detail.get('total_quantity', 0)
            cost = detail.get('total_cost', 0)
            total_cost += cost
            
            print(f"  食材: {food_name}")
            print(f"    总消耗量: {quantity}")
            print(f"    总成本: {cost:.2f}")
            
            # 显示消耗详情
            inventory_consume = detail.get('inventory_consume', {})
            order_consume = detail.get('order_consume', {})
            
            if inventory_consume.get('quantity', 0) > 0:
                print(f"    库存消耗: {inventory_consume['quantity']}, 成本: {inventory_consume['cost']:.2f}")
            
            if order_consume.get('quantity', 0) > 0:
                print(f"    订单消耗: {order_consume['quantity']}, 成本: {order_consume['cost']:.2f}")
            print()
        
        print(f"总成本: {total_cost:.2f}")

        # 3. 对比：如果使用日期查询会找到多少记录
        print("3. 对比：使用日期范围查询的记录数量:")
        start_of_day = datetime.combine(test_date, datetime.min.time())
        end_of_day = datetime.combine(test_date, datetime.max.time())
        
        date_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            created_at__gte=start_of_day,
            created_at__lte=end_of_day
        ).all()
        
        print(f"日期范围查询找到 {len(date_records)} 条记录")
        print(f"recipe_id 查询找到 {len(recipe_records)} 条记录")
        
        # 检查是否有记录在不同日期
        different_date_count = 0
        for record in recipe_records:
            record_date = record.created_at.date()
            if record_date != test_date:
                different_date_count += 1
                print(f"  记录ID {record.id} 的日期是 {record_date}，与报告日期 {test_date} 不同")
        
        if different_date_count > 0:
            print(f"发现 {different_date_count} 条记录的日期与报告日期不同")
            print("这证明了使用 recipe_id 查询比日期查询更准确")
        else:
            print("所有记录的日期都与报告日期相同")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    # 初始化数据库连接
    await Tortoise.init(
        db_url="sqlite://db.sqlite3",
        modules={"models": ["app.models"]}
    )
    
    try:
        await test_recipe_cost_query()
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
