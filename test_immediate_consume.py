#!/usr/bin/env python3
"""
测试入库时立即消耗的库存变化记录
验证price和recipe_id是否正确记录
"""

import asyncio
from datetime import date, datetime
from tortoise import Tortoise

from app.models.food_stuff import FoodStuffStoreRecord


async def test_immediate_consume_records():
    """测试立即消耗记录"""
    print("开始测试入库时立即消耗的库存变化记录...")
    
    # 测试日期
    test_date = date.today()
    school_id = 1
    
    print(f"测试日期: {test_date}")
    print(f"学校ID: {school_id}")
    
    try:
        # 查找当日的立即消耗记录
        consume_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            created_at__date=test_date,
            operation_type="下单库存消耗",
            remark__contains="入库后立即消耗"
        ).all()
        
        print(f"\n找到 {len(consume_records)} 条立即消耗记录:")
        
        for record in consume_records:
            print(f"\n=== 记录ID: {record.id} ===")
            print(f"操作类型: {record.operation_type}")
            print(f"备注: {record.remark}")
            print(f"订单ID: {record.order_id}")
            print(f"食谱ID: {record.recipe_id}")
            print(f"创建时间: {record.created_at}")
            
            print(f"食材数据详情:")
            for i, food_data in enumerate(record.food_stuff_data):
                print(f"  食材 {i+1}:")
                print(f"    食材ID: {food_data.get('food_stuff_id')}")
                print(f"    食材名称: {food_data.get('food_stuff_name')}")
                print(f"    变化量: {food_data.get('change_count')}")
                print(f"    库存价格: {food_data.get('price', '未设置')}")
                print(f"    库存订单ID: {food_data.get('order_id', '未设置')}")
                print(f"    库存食谱ID: {food_data.get('recipe_id', '未设置')}")
                print(f"    消耗订单ID: {food_data.get('consume_order_id', '未设置')}")
                print(f"    消耗食谱ID: {food_data.get('consume_recipe_id', '未设置')}")
                print(f"    消耗价格: {food_data.get('consume_price', '未设置')}")
                print(f"    订单ID列表: {food_data.get('order_ids', '未设置')}")
                print(f"    食谱ID列表: {food_data.get('recipe_ids', '未设置')}")
                
                # 验证关键字段
                issues = []
                if food_data.get('price') is None or food_data.get('price') == 0:
                    issues.append("缺少库存价格")
                if food_data.get('consume_order_id') is None:
                    issues.append("缺少消耗订单ID")
                if food_data.get('consume_recipe_id') is None:
                    issues.append("缺少消耗食谱ID")
                
                if issues:
                    print(f"    ⚠️  问题: {', '.join(issues)}")
                else:
                    print(f"    ✅ 数据完整")
                print()
        
        # 统计分析
        total_items = sum(len(record.food_stuff_data) for record in consume_records)
        complete_items = 0
        
        for record in consume_records:
            for food_data in record.food_stuff_data:
                if (food_data.get('price', 0) > 0 and 
                    food_data.get('consume_order_id') and 
                    food_data.get('consume_recipe_id')):
                    complete_items += 1
        
        print(f"\n=== 统计分析 ===")
        print(f"总记录数: {len(consume_records)}")
        print(f"总食材项数: {total_items}")
        print(f"数据完整的食材项: {complete_items}")
        print(f"数据完整率: {(complete_items/total_items*100):.1f}%" if total_items > 0 else "无数据")
        
        # 查找对应的入库记录
        print(f"\n=== 对应的入库记录 ===")
        order_ids = [record.order_id for record in consume_records if record.order_id]
        if order_ids:
            inbound_records = await FoodStuffStoreRecord.filter(
                school_id=school_id,
                order_id__in=order_ids,
                operation_type="入库"
            ).all()
            
            print(f"找到 {len(inbound_records)} 条对应的入库记录")
            for record in inbound_records:
                print(f"  入库记录ID: {record.id}, 订单ID: {record.order_id}, 食谱ID: {record.recipe_id}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    # 初始化数据库连接
    from app.settings.config import settings
    
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        await test_immediate_consume_records()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
