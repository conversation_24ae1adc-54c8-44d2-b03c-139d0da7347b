# 简化成本核算逻辑 - 基于负数库存变化和Recipe ID

## 核心思路

您的建议非常正确！成本核算应该直接从**数量为负的库存变化记录**和**recipe_id**来查找，这样更加直接、准确、简洁。

## 新的核算逻辑

### 1. 核心原则
- **只关注消耗**：只处理 `change_count < 0` 的记录（负数表示消耗）
- **Recipe ID 过滤**：根据 recipe_id 精确过滤相关消耗
- **直接统计**：从库存变化记录中直接获取价格和数量信息
- **简化分类**：减少复杂的条件判断

### 2. 查询逻辑
```python
# 查找当日所有库存变化记录
all_records = await FoodStuffStoreRecord.filter(
    school_id=school_id,
    created_at__gte=start_of_day,
    created_at__lte=end_of_day
).all()

# 遍历所有记录的 food_stuff_data
for record in all_records:
    for food_data in record.food_stuff_data:
        change_count = food_data.get('change_count', 0)
        
        # 只处理消耗（负数变化）
        if change_count >= 0:
            continue
            
        # Recipe ID 匹配检查
        actual_recipe_id = food_data.get('consume_recipe_id') or food_data.get('recipe_id') or record.recipe_id
        if recipe_id and actual_recipe_id != recipe_id:
            continue
            
        # 直接统计消耗
        consume_quantity = abs(change_count)
        price = food_data.get('price', 0.0) or food_data.get('consume_price', 0.0)
        cost = consume_quantity * price
```

### 3. Recipe ID 优先级
1. **consume_recipe_id**: 消耗目标食谱ID（立即消耗情况）
2. **food_data.recipe_id**: 食材数据中的食谱ID
3. **record.recipe_id**: 记录级别的食谱ID

## 主要改进

### 1. 简化数据结构
**旧结构**（复杂）:
```python
{
    'inventory_consume': {'quantity': 0.0, 'cost': 0.0, 'orders': []},
    'order_consume': {'quantity': 0.0, 'cost': 0.0, 'orders': []}
}
```

**新结构**（简洁）:
```python
{
    'food_stuff_id': 1,
    'food_stuff_name': '大米',
    'unit_name': '公斤',
    'total_quantity': 50.0,      # 总消耗数量
    'total_cost': 250.0,         # 总消耗成本
    'consume_details': [...]     # 详细消耗记录
}
```

### 2. 统一处理逻辑
**旧逻辑**（复杂）:
- 区分操作类型
- 检查立即消耗标记
- 分别处理库存消耗和订单消耗
- 重复的价格获取逻辑

**新逻辑**（简洁）:
- 只关注负数变化
- 统一的价格获取策略
- 直接累计数量和成本
- 后期按需分类

### 3. 价格获取策略
```python
# 优先级顺序
price = (
    food_data.get('consume_price', 0.0) or      # 消耗订单价格
    food_data.get('price', 0.0) or              # 库存价格
    await self._get_food_stuff_avg_price(...) or # 平均价格
    10.0                                         # 默认价格
)
```

## 核心代码实现

### 主要处理逻辑
```python
async def _calculate_daily_cost(self, report_date: date, school_id: int, recipe_id: int):
    """计算指定日期的成本明细 - 基于库存变化记录中的负数消耗"""
    
    # 查找当日所有库存变化记录
    all_records = await FoodStuffStoreRecord.filter(
        school_id=school_id,
        created_at__gte=start_of_day,
        created_at__lte=end_of_day
    ).all()
    
    food_stuff_costs = defaultdict(lambda: {
        'food_stuff_id': 0,
        'food_stuff_name': '',
        'unit_name': '',
        'total_quantity': 0.0,
        'total_cost': 0.0,
        'consume_details': []
    })
    
    # 处理所有库存变化记录，只关注消耗（负数变化）
    for record in all_records:
        for food_data in record.food_stuff_data:
            change_count = food_data.get('change_count', 0)
            
            # 只处理消耗（负数变化）
            if change_count >= 0:
                continue
            
            # Recipe ID 匹配检查
            actual_recipe_id = (
                food_data.get('consume_recipe_id') or 
                food_data.get('recipe_id') or 
                record.recipe_id
            )
            if recipe_id and actual_recipe_id != recipe_id:
                continue
            
            # 计算消耗
            consume_quantity = abs(change_count)
            price = (
                food_data.get('consume_price', 0.0) or
                food_data.get('price', 0.0) or
                await self._get_food_stuff_avg_price(food_stuff_id, school_id, report_date) or
                10.0
            )
            cost = consume_quantity * price
            
            # 累计统计
            food_stuff_costs[food_stuff_id]['total_quantity'] += consume_quantity
            food_stuff_costs[food_stuff_id]['total_cost'] += cost
            
            # 记录详细信息
            food_stuff_costs[food_stuff_id]['consume_details'].append({
                'record_id': record.id,
                'operation_type': record.operation_type,
                'quantity': consume_quantity,
                'price': price,
                'cost': cost,
                'recipe_id': actual_recipe_id,
                # ... 其他详细信息
            })
    
    return list(food_stuff_costs.values())
```

## 优势对比

### 1. 代码简洁性
- **旧版本**: ~120行复杂逻辑
- **新版本**: ~70行清晰逻辑
- **减少**: 40%+ 代码量

### 2. 逻辑清晰性
- **直接目标**: 只关注消耗记录
- **统一处理**: 不区分消耗类型
- **精确过滤**: Recipe ID 精确匹配

### 3. 性能优化
- **减少查询**: 不需要额外的关联查询
- **减少判断**: 简化条件分支
- **直接统计**: 避免重复计算

### 4. 维护性
- **易于理解**: 逻辑直观明了
- **易于调试**: 详细的消耗记录
- **易于扩展**: 简单的数据结构

## 兼容性处理

### 1. 向后兼容
- 保持原有的返回格式（inventory_consume, order_consume）
- 在结果转换阶段进行分类
- 不影响现有的前端和API

### 2. 数据完整性
- 处理缺失的价格信息
- 处理缺失的Recipe ID
- 提供详细的调试信息

## 测试验证

### 1. 测试重点
- **消耗识别**: 确保只统计负数变化
- **Recipe过滤**: 验证Recipe ID过滤准确性
- **价格计算**: 验证价格获取优先级
- **数据完整**: 验证所有字段正确填充

### 2. 测试脚本
- 更新 `test_cost_accounting.py`
- 显示详细的消耗记录
- 验证总成本计算准确性

## 总结

新的成本核算逻辑完全基于您的建议：
1. **直接查找负数库存变化** - 精确识别消耗
2. **Recipe ID 精确过滤** - 避免无关数据
3. **简化处理逻辑** - 提高代码质量
4. **统一数据来源** - 确保数据一致性

这种方法更加直接、准确、易维护，完全符合成本核算的本质需求。
