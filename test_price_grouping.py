#!/usr/bin/env python3
"""
测试按食材+价格分组的成本核算逻辑
验证同一食材不同价格的消耗会分开显示
"""

import asyncio
from datetime import date, datetime
from tortoise import Tortoise

from app.models.food_stuff import FoodStuffStoreRecord
from app.controllers.daily_cost_report import daily_cost_report_controller


async def test_price_grouping():
    """测试价格分组逻辑"""
    print("开始测试按食材+价格分组的成本核算...")

    # 测试参数
    school_id = 1
    recipe_id = 1
    test_date = date.today()

    print(f"测试参数:")
    print(f"  学校ID: {school_id}")
    print(f"  食谱ID: {recipe_id}")
    print(f"  报告日期: {test_date}")

    try:
        # 1. 查看与该 recipe_id 相关的库存变化记录
        print("\n1. 查找与 recipe_id 相关的库存变化记录:")
        recipe_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            recipe_id=recipe_id
        ).all()
        
        print(f"找到 {len(recipe_records)} 条与食谱 {recipe_id} 相关的记录")
        
        # 分析记录中的价格分布
        food_price_map = {}  # {food_stuff_id: [prices]}
        
        for record in recipe_records:
            print(f"\n记录ID: {record.id}, 操作类型: {record.operation_type}")
            
            for food_data in record.food_stuff_data:
                change_count = food_data.get('change_count', 0)
                if change_count < 0:  # 只关注消耗记录
                    food_stuff_id = food_data.get('food_stuff_id')
                    food_stuff_name = food_data.get('food_stuff_name', f'食材{food_stuff_id}')
                    price = food_data.get('price', 0.0)
                    quantity = abs(change_count)
                    
                    print(f"  消耗: {food_stuff_name} (ID: {food_stuff_id})")
                    print(f"    数量: {quantity}")
                    print(f"    价格: {price}")
                    print(f"    成本: {quantity * price}")
                    
                    # 记录价格分布
                    if food_stuff_id not in food_price_map:
                        food_price_map[food_stuff_id] = {'name': food_stuff_name, 'prices': []}
                    food_price_map[food_stuff_id]['prices'].append(price)

        # 2. 分析价格分布情况
        print("\n2. 食材价格分布分析:")
        multi_price_foods = []
        for food_id, info in food_price_map.items():
            unique_prices = list(set(info['prices']))
            print(f"  {info['name']} (ID: {food_id}): {len(unique_prices)} 种价格")
            for price in unique_prices:
                count = info['prices'].count(price)
                print(f"    价格 {price}: {count} 次消耗")
            
            if len(unique_prices) > 1:
                multi_price_foods.append((food_id, info['name'], unique_prices))

        if multi_price_foods:
            print(f"\n发现 {len(multi_price_foods)} 种食材有多个不同价格:")
            for food_id, name, prices in multi_price_foods:
                print(f"  {name}: {prices}")
        else:
            print("\n所有食材都只有单一价格")

        # 3. 使用新的成本计算方法
        print("\n3. 使用按价格分组的成本计算方法:")
        cost_details = await daily_cost_report_controller._calculate_daily_cost(
            test_date, school_id, recipe_id
        )
        
        print(f"计算结果: 找到 {len(cost_details)} 条成本记录")
        
        total_cost = 0.0
        food_groups = {}  # {food_stuff_id: [records]}
        
        for detail in cost_details:
            food_id = detail.get('food_stuff_id')
            food_name = detail.get('food_stuff_name', '未知食材')
            price = detail.get('price', 0)
            quantity = detail.get('total_quantity', 0)
            cost = detail.get('total_cost', 0)
            total_cost += cost

            print(f"\n  记录: {food_name} (ID: {food_id})")
            print(f"    单价: {price}")
            print(f"    消耗量: {quantity}")
            print(f"    成本: {cost:.2f}")
            
            # 按食材ID分组统计
            if food_id not in food_groups:
                food_groups[food_id] = {'name': food_name, 'records': []}
            food_groups[food_id]['records'].append({
                'price': price,
                'quantity': quantity,
                'cost': cost
            })
            
            # 显示消耗详情
            inventory_consume = detail.get('inventory_consume', {})
            order_consume = detail.get('order_consume', {})
            
            if inventory_consume.get('quantity', 0) > 0:
                print(f"    库存消耗: {inventory_consume['quantity']}, 成本: {inventory_consume['cost']:.2f}")
            
            if order_consume.get('quantity', 0) > 0:
                print(f"    订单消耗: {order_consume['quantity']}, 成本: {order_consume['cost']:.2f}")

        print(f"\n总成本: {total_cost:.2f}")

        # 4. 验证分组效果
        print("\n4. 验证分组效果:")
        for food_id, info in food_groups.items():
            records = info['records']
            if len(records) > 1:
                print(f"  {info['name']} 被分成了 {len(records)} 条记录:")
                for i, record in enumerate(records, 1):
                    print(f"    记录{i}: 单价{record['price']}, 数量{record['quantity']}, 成本{record['cost']:.2f}")
            else:
                print(f"  {info['name']}: 单一价格记录")

        # 5. 对比总结
        print("\n5. 分组效果总结:")
        original_food_count = len(food_price_map)
        grouped_record_count = len(cost_details)
        
        print(f"  原始食材种类数: {original_food_count}")
        print(f"  分组后记录数: {grouped_record_count}")
        
        if grouped_record_count > original_food_count:
            print(f"  成功实现价格分组: 增加了 {grouped_record_count - original_food_count} 条记录")
        else:
            print(f"  未发现价格分组效果")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    # 初始化数据库连接
    await Tortoise.init(
        db_url="sqlite://db.sqlite3",
        modules={"models": ["app.models"]}
    )
    
    try:
        await test_price_grouping()
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
