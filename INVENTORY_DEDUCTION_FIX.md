# 库存扣除记录修复 - 添加订单ID和价格信息

## 问题描述

在下单时直接扣除库存的过程中，库存变化记录缺少以下关键信息：
1. **order_id**: 订单ID信息
2. **price**: 价格信息
3. **recipe_id**: 食谱ID信息

这导致成本核算时无法准确追溯库存消耗的来源和成本。

## 根本原因

### 1. `_deduct_inventory_fifo` 方法问题
- **文件**: `app/controllers/order.py`
- **问题**: 在记录库存变化时，只记录了基本字段（`food_stuff_id`, `before_count`, `change_count`）
- **缺失**: 没有从被扣除的库存记录中获取 `price`, `order_id`, `recipe_id` 信息

### 2. `_deduct_inventory_fifo_for_consume` 方法问题
- **文件**: `app/controllers/food_stuff_store.py`
- **问题**: 同样只记录基本字段，缺少价格和订单追溯信息

### 3. 合并记录处理不完善
- **文件**: `app/controllers/order.py` 中的 `_process_inventory_and_order` 方法
- **问题**: 合并多个库存扣除记录时，没有正确处理价格和订单信息

## 修复方案

### 1. 增强 `_deduct_inventory_fifo` 方法

**修改位置**: `app/controllers/order.py` 第137-145行和第159-167行

**修改内容**:
```python
# 记录库存变化时添加完整信息
inventory_changes.append({
    "food_stuff_id": food_stuff_id,
    "before_count": before_count,
    "change_count": -remaining_quantity,
    "price": store.price,  # 添加价格信息
    "order_id": store.order_id,  # 添加订单ID
    "recipe_id": store.recipe_id  # 添加食谱ID
})
```

### 2. 增强 `_deduct_inventory_fifo_for_consume` 方法

**修改位置**: `app/controllers/food_stuff_store.py` 第441-449行和第463-471行

**修改内容**: 同样添加价格、订单ID、食谱ID信息到库存变化记录中

### 3. 改进合并记录逻辑

**修改位置**: `app/controllers/order.py` 第68-101行

**新增功能**:
- 计算加权平均价格
- 收集所有相关的订单ID和食谱ID
- 在合并记录中保存完整的追溯信息

**核心逻辑**:
```python
# 计算加权平均价格
total_cost = 0
total_quantity = 0
order_ids = []
recipe_ids = []

for change in deduction_result['inventory_changes']:
    quantity = abs(change['change_count'])
    price = change.get('price', 0)
    total_cost += quantity * price
    total_quantity += quantity
    
    if change.get('order_id'):
        order_ids.append(change['order_id'])
    if change.get('recipe_id'):
        recipe_ids.append(change['recipe_id'])

# 计算平均价格
avg_price = total_cost / total_quantity if total_quantity > 0 else 0

# 添加合并记录
inventory_changes.append({
    "food_stuff_id": food_stuff_id,
    "before_count": initial_store,
    "change_count": total_change,
    "price": avg_price,  # 加权平均价格
    "order_ids": list(set(order_ids)),  # 去重的订单ID列表
    "recipe_ids": list(set(recipe_ids))  # 去重的食谱ID列表
})
```

### 4. 增强 `record_store_change` 方法

**修改位置**: `app/controllers/food_stuff_store.py` 第221-243行

**新增功能**:
- 支持处理订单ID和食谱ID列表
- 在food_stuff_data中保存完整的追溯信息

**处理逻辑**:
```python
# 处理订单ID和食谱ID（可能是单个值或列表）
change_order_id = change.get("order_id", order_id)
change_recipe_id = change.get("recipe_id", recipe_id)

# 如果是列表，取第一个作为主要ID
if isinstance(change.get("order_ids"), list) and change["order_ids"]:
    change_order_id = change["order_ids"][0]
if isinstance(change.get("recipe_ids"), list) and change["recipe_ids"]:
    change_recipe_id = change["recipe_ids"][0]

food_item = {
    "food_stuff_id": change["food_stuff_id"],
    "food_stuff_name": food_stuff.food_stuff_name,
    "before_count": change["before_count"],
    "change_count": change["change_count"],
    "after_count": change["before_count"] + change["change_count"],
    "unit_name": unit_name,
    "price": change.get("price", 0.0),  # 价格信息
    "order_id": change_order_id,  # 主要订单ID
    "recipe_id": change_recipe_id,  # 主要食谱ID
    "order_ids": change.get("order_ids", [change_order_id] if change_order_id else []),  # 所有相关订单ID
    "recipe_ids": change.get("recipe_ids", [change_recipe_id] if change_recipe_id else [])  # 所有相关食谱ID
}
```

## 修复效果

### 1. 完整的成本追溯
- 每条库存变化记录都包含准确的价格信息
- 可以追溯到具体的源订单
- 支持多订单合并扣除的情况

### 2. 准确的成本核算
- 基于实际的库存价格计算成本
- 避免使用默认价格导致的误差
- 支持加权平均价格计算

### 3. 数据完整性
- 库存变化记录包含完整的上下文信息
- 支持复杂的成本分析需求
- 便于后续的审计和追溯

## 测试验证

### 1. 更新测试脚本
- **文件**: `test_cost_accounting.py`
- **新增**: 详细检查库存变化记录中的价格和订单信息
- **验证**: 确保所有字段都被正确记录

### 2. 测试场景
1. **单一库存扣除**: 验证单个库存记录扣除时的信息完整性
2. **多库存合并扣除**: 验证多个库存记录合并扣除时的价格计算
3. **立即消耗**: 验证入库后立即消耗的记录完整性

### 3. 验证要点
- 价格信息是否正确记录
- 订单ID是否正确关联
- 食谱ID是否正确传递
- 加权平均价格计算是否准确

## 注意事项

1. **向后兼容**: 修改保持了向后兼容性，旧记录仍可正常处理
2. **性能影响**: 增加的字段和计算对性能影响很小
3. **数据一致性**: 确保所有库存操作都使用统一的记录格式

## 后续建议

1. **监控验证**: 在生产环境中监控新记录的数据完整性
2. **历史数据**: 考虑为历史数据补充缺失的价格和订单信息
3. **报表优化**: 基于新的完整数据优化成本报表和分析功能
