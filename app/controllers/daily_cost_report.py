from tortoise.expressions import Q
from tortoise.transactions import atomic
from datetime import date
from typing import List, Dict, Any
from collections import defaultdict

from app.core.crud import CRUDBase
from app.models.food_stuff import DailyCostReport, FoodStuffStore, FoodStuff, Unit
from app.models.food_menu import Recipe, RecipeMeal
from app.models.orders import Order, OrderStatus
from app.schemas.daily_cost_report import DailyCostReportCreate, DailyCostReportUpdate, DailyCostReportSearch, DailyCostReportGenerate
from app.controllers.utils import get_school_id


class DailyCostReportController(CRUDBase[DailyCostReport, DailyCostReportCreate, DailyCostReportUpdate]):
    def __init__(self):
        super().__init__(model=DailyCostReport)

    async def get_report_list(self, search: DailyCostReportSearch):
        """获取日成本核算单列表"""
        try:
            school_id = await get_school_id()
        except Exception:
            return {
                "total": 0,
                "data": []
            }

        q = Q(school_id=school_id)
        
        if search.report_date:
            q &= Q(report_date=search.report_date)
        
        if search.start_date and search.end_date:
            q &= Q(report_date__gte=search.start_date, report_date__lte=search.end_date)
        elif search.start_date:
            q &= Q(report_date__gte=search.start_date)
        elif search.end_date:
            q &= Q(report_date__lte=search.end_date)
            
        if search.status:
            q &= Q(status=search.status)

        total = await self.model.filter(q).count()
        
        offset = (search.current - 1) * search.size
        reports = await self.model.filter(q).order_by("-report_date").offset(offset).limit(search.size)
        
        # 获取关联的食谱信息
        data = []
        for report in reports:
            report_dict = await report.to_dict()
            
            # 获取食谱信息
            if report.recipe_id:
                recipe = await Recipe.get_or_none(id=report.recipe_id)
                if recipe:
                    report_dict['recipe_name'] = recipe.recipe_name
                else:
                    report_dict['recipe_name'] = None
            else:
                report_dict['recipe_name'] = None
                
            data.append(report_dict)

        return {
            "total": total,
            "data": data
        }

    async def get_report_detail(self, report_id: int):
        """获取日成本核算单详情"""
        school_id = await get_school_id()

        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")

        report_dict = await report.to_dict()

        # 确保cost_details字段被正确处理
        if hasattr(report, 'cost_details') and report.cost_details:
            report_dict['cost_details'] = report.cost_details
            print("=== get_report_detail - 成本明细数据 ===")
            for i, detail in enumerate(report.cost_details):
                print(f"食材 {i+1}: {detail.get('food_stuff_name')} - 价格: {detail.get('price')} - 数量: {detail.get('total_quantity')} - 成本: {detail.get('total_cost')}")
            print("=== 成本明细数据结束 ===")

        # 获取食谱信息
        if report.recipe_id:
            recipe = await Recipe.get_or_none(id=report.recipe_id)
            if recipe:
                report_dict['recipe_name'] = recipe.recipe_name
                # 获取餐次信息
                meals = await RecipeMeal.filter(recipe=recipe).all()
                report_dict['meals'] = []
                for meal in meals:
                    meal_dict = await meal.to_dict()
                    report_dict['meals'].append(meal_dict)
            else:
                report_dict['recipe_name'] = None
                report_dict['meals'] = []
        else:
            report_dict['recipe_name'] = None
            report_dict['meals'] = []

        return report_dict

    @atomic()
    async def generate_report(self, obj_in: DailyCostReportGenerate):
        """生成日成本核算单"""
        school_id = await get_school_id()
        print("生成核算单", obj_in.model_dump())
        # 检查是否已存在该日期的核算单
        existing_report = await self.model.filter(
            report_date=obj_in.report_date,
            school_id=school_id
        ).first()
        
        if existing_report:
            raise ValueError(f"日期 {obj_in.report_date} 的核算单已存在")
        
        # 获取食谱信息
        recipe_id = obj_in.recipe_id
        if not recipe_id:
            # 自动查找当日食谱
            recipe = await Recipe.filter(
                recipe_date=obj_in.report_date,
                school_id=school_id,
                is_active=True
            ).first()
            if recipe:
                recipe_id = recipe.id
        
        if not recipe_id:
            raise ValueError(f"未找到日期 {obj_in.report_date} 的食谱")
        
        # 获取当日所有相关的库存消耗和订单消耗
        cost_details = await self._calculate_daily_cost(obj_in.report_date, school_id, recipe_id)
        
        # 计算总成本
        total_cost = 0.0
        inventory_cost = 0.0
        order_cost = 0.0

        for detail in cost_details:
            if 'inventory_consume' in detail and detail['inventory_consume']:
                inventory_cost += float(detail['inventory_consume'].get('cost', 0.0))
            if 'order_consume' in detail and detail['order_consume']:
                order_cost += float(detail['order_consume'].get('cost', 0.0))

        total_cost = inventory_cost + order_cost
        
        # 创建核算单
        report = DailyCostReport(
            report_date=obj_in.report_date,
            school_id=school_id,
            recipe_id=recipe_id,
            total_cost=total_cost,
            inventory_cost=inventory_cost,
            order_cost=order_cost,
            cost_details=cost_details,
            status="draft"
        )
        await report.save()
        
        return report

    async def _calculate_daily_cost(self, report_date: date, school_id: int, recipe_id: int) -> List[Dict[str, Any]]:
        """计算指定食谱的成本明细 - 基于recipe_id查找库存变化记录中的负数消耗，按食材+价格分组显示"""
        from app.models.food_stuff import FoodStuffStoreRecord
        import logging
        logger = logging.getLogger(__name__)

        # 直接通过 recipe_id 查找相关的库存变化记录
        # 因为一个 recipe_id 只对应一天的食材和菜品，不需要严格匹配日期
        all_records = await FoodStuffStoreRecord.filter(
            school_id=school_id,
            recipe_id=recipe_id
        ).all()

        logger.info(f"查找食谱ID {recipe_id} 的库存记录，学校ID: {school_id}")
        logger.info(f"找到 {len(all_records)} 条库存记录")

        # 按食材ID+价格分组统计消耗，这样不同价格的同一食材会分开显示
        food_stuff_costs = defaultdict(lambda: {
            'food_stuff_id': 0,
            'food_stuff_name': '',
            'unit_name': '',
            'price': 0.0,
            'total_quantity': 0.0,
            'total_cost': 0.0,
            'consume_details': []  # 详细的消耗记录
        })

        # 处理所有库存变化记录，只关注消耗（负数变化）
        for record in all_records:
            logger.info(f"处理记录ID: {record.id}, 操作类型: {record.operation_type}, 订单ID: {record.order_id}, 食谱ID: {record.recipe_id}")

            for food_data in record.food_stuff_data:
                food_stuff_id = food_data.get('food_stuff_id')
                if not food_stuff_id:
                    continue

                change_count = food_data.get('change_count', 0)

                # 只处理消耗（负数变化）
                if change_count >= 0:
                    continue

                # 获取食材的recipe_id信息
                data_recipe_id = food_data.get('recipe_id', record.recipe_id)
                consume_recipe_id = food_data.get('consume_recipe_id')

                # 确定实际的recipe_id（优先使用consume_recipe_id，因为它表示消耗的目标食谱）
                actual_recipe_id = consume_recipe_id or data_recipe_id

                # 由于我们已经通过 recipe_id 直接查询了记录，不需要再次过滤

                consume_quantity = abs(change_count)
                price = food_data.get('price', 0.0)

                # 如果没有价格信息，尝试获取
                if price <= 0:
                    price = await self._get_food_stuff_avg_price(food_stuff_id, school_id, report_date)
                    if price <= 0:
                        price = 10.0  # 默认价格
                        logger.warning(f"食材 {food_stuff_id} 使用默认价格: {price}")

                # 优先使用消耗订单的价格
                consume_price = food_data.get('consume_price', 0.0)
                if consume_price > 0:
                    price = consume_price

                cost = consume_quantity * price

                # 使用食材ID+价格作为分组键，这样不同价格的同一食材会分开统计
                group_key = f"{food_stuff_id}_{price}"

                # 初始化食材成本数据
                if food_stuff_costs[group_key]['food_stuff_id'] == 0:
                    food_stuff_costs[group_key]['food_stuff_id'] = food_stuff_id
                    food_stuff_costs[group_key]['food_stuff_name'] = food_data.get('food_stuff_name', '')
                    food_stuff_costs[group_key]['unit_name'] = food_data.get('unit_name', '')
                    food_stuff_costs[group_key]['price'] = price

                # 累计数量和成本
                food_stuff_costs[group_key]['total_quantity'] += consume_quantity
                food_stuff_costs[group_key]['total_cost'] += cost

                # 记录详细信息
                consume_detail = {
                    'record_id': record.id,
                    'operation_type': record.operation_type,
                    'quantity': consume_quantity,
                    'price': price,
                    'cost': cost,
                    'order_id': food_data.get('order_id', record.order_id),
                    'recipe_id': actual_recipe_id,
                    'consume_order_id': food_data.get('consume_order_id'),
                    'consume_recipe_id': consume_recipe_id,
                    'remark': record.remark,
                    'created_at': record.created_at
                }
                food_stuff_costs[group_key]['consume_details'].append(consume_detail)

                logger.info(f"消耗记录: 食材ID {food_stuff_id}, 数量 {consume_quantity}, 价格 {price}, 成本 {cost}, 食谱ID {actual_recipe_id}")

        # 转换为最终结果格式
        result = []
        for group_key, data in food_stuff_costs.items():
            if data['total_quantity'] > 0:
                # 按消耗类型分组统计
                inventory_consume = {'quantity': 0.0, 'cost': 0.0, 'orders': []}
                order_consume = {'quantity': 0.0, 'cost': 0.0, 'orders': []}

                for detail in data['consume_details']:
                    if detail['consume_order_id']:
                        # 有消耗订单ID，归类为订单消耗
                        order_consume['quantity'] += detail['quantity']
                        order_consume['cost'] += detail['cost']
                        order_consume['orders'].append({
                            'order_id': detail['consume_order_id'],
                            'quantity': detail['quantity'],
                            'price': detail['price'],
                            'recipe_id': detail['consume_recipe_id']
                        })
                    else:
                        # 普通库存消耗
                        inventory_consume['quantity'] += detail['quantity']
                        inventory_consume['cost'] += detail['cost']
                        inventory_consume['orders'].append({
                            'order_id': detail['order_id'],
                            'quantity': detail['quantity'],
                            'price': detail['price'],
                            'recipe_id': detail['recipe_id']
                        })

                logger.info(f"添加结果记录: 食材ID {data['food_stuff_id']}, 名称 {data['food_stuff_name']}, 价格 {data['price']}")

                result.append({
                    'food_stuff_id': data['food_stuff_id'],
                    'food_stuff_name': data['food_stuff_name'],
                    'unit_name': data['unit_name'],
                    'price': data['price'],  # 单价字段
                    'total_quantity': data['total_quantity'],
                    'total_cost': data['total_cost'],
                    'inventory_consume': inventory_consume,
                    'order_consume': order_consume,
                    'consume_details': data['consume_details']  # 保留详细信息用于调试
                })

        logger.info(f"最终计算结果: 找到 {len(result)} 种食材的消耗记录")

        return result

    async def _get_food_stuff_avg_price(self, food_stuff_id: int, school_id: int, report_date: date) -> float:
        """获取食材在指定日期的平均价格"""
        import logging
        logger = logging.getLogger(__name__)

        # 获取当日及之前的库存记录，按时间倒序
        stores = await FoodStuffStore.filter(
            food_stuff_id=food_stuff_id,
            school_id=school_id,
            created_at__date__lte=report_date
        ).order_by('-created_at').limit(10)  # 取最近10条记录计算平均价格

        logger.info(f"查找食材 {food_stuff_id} 的价格，找到 {len(stores)} 条库存记录")

        if not stores:
            # 如果没有找到库存记录，尝试从最近的订单中获取价格
            recent_orders = await Order.filter(
                school_id=school_id,
                order_status=OrderStatus.CONFIRMED
            ).order_by('-created_at').limit(5)

            for order in recent_orders:
                for item in order.order_items:
                    if item.get('id') == food_stuff_id and item.get('price'):
                        price = float(item.get('price', 0))
                        logger.info(f"从订单 {order.id} 中找到食材 {food_stuff_id} 的价格: {price}")
                        return price

            logger.warning(f"食材 {food_stuff_id} 没有找到任何价格信息")
            return 0.0

        # 过滤掉价格为0的记录
        valid_stores = [store for store in stores if store.price > 0]

        if not valid_stores:
            logger.warning(f"食材 {food_stuff_id} 的所有库存记录价格都为0")
            return 0.0

        total_cost = sum(store.price for store in valid_stores)
        avg_price = total_cost / len(valid_stores)
        logger.info(f"食材 {food_stuff_id} 的平均价格: {avg_price}")
        return avg_price

    @atomic()
    async def confirm_report(self, report_id: int):
        """确认核算单"""
        school_id = await get_school_id()
        
        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")
        
        if report.status == "confirmed":
            raise ValueError("核算单已确认，无法重复确认")
        
        report.status = "confirmed"
        await report.save()
        
        return report

    @atomic()
    async def delete_report(self, report_id: int):
        """删除核算单"""
        school_id = await get_school_id()
        
        report = await self.model.filter(id=report_id, school_id=school_id).first()
        if not report:
            raise ValueError("核算单不存在或不属于当前学校")
        
        if report.status == "confirmed":
            raise ValueError("已确认的核算单无法删除")
        
        await report.delete()


daily_cost_report_controller = DailyCostReportController()
